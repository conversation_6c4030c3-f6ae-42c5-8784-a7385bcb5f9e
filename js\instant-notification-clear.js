/**
 * Instant Notification Clear
 * Run this script to immediately clear notification badges and counts
 */

// Function to instantly clear notification badge
function instantClearNotificationBadge() {
    console.log('🔔 Clearing notification badges...');
    
    // 1. Hide the header notification badge immediately
    const headerBadge = document.getElementById('headerNotificationBadge');
    if (headerBadge) {
        headerBadge.style.display = 'none';
        headerBadge.textContent = '0';
        console.log('✅ Header badge hidden');
    }
    
    // 2. Hide all notification badges
    const allBadges = document.querySelectorAll('.badge.bg-warning, .badge.bg-danger, .cart-count');
    allBadges.forEach(badge => {
        if (badge.textContent === '4' || badge.closest('a[href="user_notifications.php"]')) {
            badge.style.display = 'none';
            console.log('✅ Badge hidden:', badge);
        }
    });
    
    // 3. Hide dropdown notification badges
    const dropdownBadges = document.querySelectorAll('a[href="user_notifications.php"] .badge');
    dropdownBadges.forEach(badge => {
        badge.style.display = 'none';
        console.log('✅ Dropdown badge hidden');
    });
    
    // 4. Clear any notification containers
    const notificationContainer = document.getElementById('notification-container');
    if (notificationContainer) {
        notificationContainer.innerHTML = '';
        console.log('✅ Notification container cleared');
    }
    
    // 5. Remove any visible toast notifications
    const toasts = document.querySelectorAll('.toastify, .toast, .alert-dismissible');
    toasts.forEach(toast => {
        if (!toast.classList.contains('alert-permanent')) {
            toast.remove();
            console.log('✅ Toast removed');
        }
    });
    
    console.log('🎉 All notification badges cleared!');
    
    // Show confirmation
    showClearConfirmation();
}

// Function to clear notifications via API
async function clearNotificationsViaAPI() {
    console.log('🌐 Clearing notifications via API...');
    
    try {
        const response = await fetch('clear_header_notifications.php?action=mark_read&format=json');
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ API clear successful:', data);
            
            // Update UI after API success
            instantClearNotificationBadge();
            
            return data;
        } else {
            console.error('❌ API clear failed:', data.message);
            return null;
        }
    } catch (error) {
        console.error('❌ API error:', error);
        return null;
    }
}

// Function to show confirmation
function showClearConfirmation() {
    // Remove any existing confirmation
    const existingConfirmation = document.getElementById('instant-clear-confirmation');
    if (existingConfirmation) {
        existingConfirmation.remove();
    }
    
    // Create confirmation element
    const confirmation = document.createElement('div');
    confirmation.id = 'instant-clear-confirmation';
    confirmation.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 99999;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
        font-size: 14px;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
    `;
    
    confirmation.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 18px;">✅</span>
            <span>Notification badges cleared!</span>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: 10px;">×</button>
        </div>
    `;
    
    // Add animation keyframes
    if (!document.getElementById('instant-clear-styles')) {
        const style = document.createElement('style');
        style.id = 'instant-clear-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(confirmation);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (confirmation.parentNode) {
            confirmation.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => confirmation.remove(), 300);
        }
    }, 5000);
}

// Function to completely reset notifications
async function completeNotificationReset() {
    console.log('🔄 Starting complete notification reset...');
    
    // Step 1: Clear via API
    const apiResult = await clearNotificationsViaAPI();
    
    // Step 2: Clear UI elements
    instantClearNotificationBadge();
    
    // Step 3: Clear any notification manager queues
    if (window.notificationManager) {
        window.notificationManager.clearAll();
        console.log('✅ Notification manager cleared');
    }
    
    // Step 4: Clear local storage notification data
    const notificationKeys = ['notifications', 'notification_count', 'last_notification_check'];
    notificationKeys.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
    });
    console.log('✅ Local storage cleared');
    
    // Step 5: Force update header notification count
    if (typeof updateHeaderNotificationCount === 'function') {
        setTimeout(updateHeaderNotificationCount, 1000);
        console.log('✅ Header update scheduled');
    }
    
    console.log('🎉 Complete notification reset finished!');
    return apiResult;
}

// Make functions globally available
window.instantClearNotificationBadge = instantClearNotificationBadge;
window.clearNotificationsViaAPI = clearNotificationsViaAPI;
window.completeNotificationReset = completeNotificationReset;

// Auto-run if this script is loaded directly
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔔 Instant notification clear loaded. Use completeNotificationReset() to clear all notifications.');
    });
} else {
    console.log('🔔 Instant notification clear loaded. Use completeNotificationReset() to clear all notifications.');
}

// Add keyboard shortcut (Ctrl+Alt+C)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.altKey && e.key === 'c') {
        e.preventDefault();
        completeNotificationReset();
    }
});

console.log(`
🔔 NOTIFICATION CLEAR COMMANDS:
• instantClearNotificationBadge() - Hide badges immediately
• clearNotificationsViaAPI() - Clear via server API
• completeNotificationReset() - Full reset (recommended)
• Keyboard shortcut: Ctrl+Alt+C
`);
