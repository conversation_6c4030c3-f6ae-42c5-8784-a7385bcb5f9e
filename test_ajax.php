<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Add to Cart</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test AJAX Add to Cart</h1>
    
    <div id="session-info">
        <h2>Session Info:</h2>
        <?php
        session_start();
        echo "<pre>";
        print_r($_SESSION);
        echo "</pre>";
        
        if (!isset($_SESSION['user_id'])) {
            echo "<p style='color: red;'>❌ Not logged in</p>";
            echo "<button onclick='login()'>Login as Test User</button>";
        } else {
            echo "<p style='color: green;'>✅ Logged in as User ID: " . $_SESSION['user_id'] . "</p>";
            echo "<button onclick='logout()'>Logout</button>";
        }
        ?>
    </div>
    
    <div id="test-section">
        <h2>Test Add to Cart:</h2>
        <button onclick="testAddToCart(29)">Add Product 29 to Cart</button>
        <button onclick="testAddToCart(30)">Add Product 30 to Cart</button>
        <button onclick="testAddToCart(999)">Add Invalid Product (999)</button>
    </div>
    
    <div id="results">
        <h2>Results:</h2>
    </div>

    <script>
        function login() {
            fetch('test_ajax.php?action=login', { method: 'POST' })
                .then(() => location.reload());
        }
        
        function logout() {
            fetch('test_ajax.php?action=logout', { method: 'POST' })
                .then(() => location.reload());
        }
        
        function testAddToCart(productId) {
            const resultsDiv = document.getElementById('results');
            
            // Add loading message
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'result';
            loadingDiv.innerHTML = `🔄 Testing add to cart for product ${productId}...`;
            resultsDiv.appendChild(loadingDiv);
            
            // Send AJAX request
            fetch('ajax/test_add_to_cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}&quantity=1`
            })
            .then(response => response.text())
            .then(text => {
                // Remove loading message
                loadingDiv.remove();
                
                // Add result
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.className += ' success';
                        resultDiv.innerHTML = `
                            <strong>✅ SUCCESS</strong><br>
                            Message: ${data.message}<br>
                            Product ID: ${data.data.product_id}<br>
                            Cart Count: ${data.data.cart_count}<br>
                            <details>
                                <summary>Debug Info</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        `;
                    } else {
                        resultDiv.className += ' error';
                        resultDiv.innerHTML = `
                            <strong>❌ FAILED</strong><br>
                            Message: ${data.message}<br>
                            <details>
                                <summary>Debug Info</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        `;
                    }
                } catch (e) {
                    resultDiv.className += ' error';
                    resultDiv.innerHTML = `
                        <strong>❌ JSON PARSE ERROR</strong><br>
                        Error: ${e.message}<br>
                        Raw Response:<br>
                        <pre>${text}</pre>
                    `;
                }
                
                resultsDiv.appendChild(resultDiv);
            })
            .catch(error => {
                // Remove loading message
                loadingDiv.remove();
                
                // Add error result
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ NETWORK ERROR</strong><br>
                    Error: ${error.message}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        }
    </script>
</body>
</html>

<?php
// Handle login/logout actions
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'login') {
        $_SESSION['user_id'] = 1;
        $_SESSION['user_name'] = 'Test User';
        $_SESSION['user_email'] = '<EMAIL>';
        echo "Logged in";
    } elseif ($_GET['action'] === 'logout') {
        session_destroy();
        echo "Logged out";
    }
    exit;
}
?>
