console.log('🚀 Add to Cart script loaded!');

// Create notification container
function createNotificationContainer() {
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
}

// Show notification
function showNotification(message, type = 'success', duration = 4000) {
    createNotificationContainer();

    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification-toast`;
    notification.style.cssText = `
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-radius: 8px;
        animation: slideInRight 0.3s ease;
    `;

    const icon = type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle';
    const iconColor = type === 'success' ? '#10b981' : type === 'danger' ? '#ef4444' : '#3b82f6';

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${icon} me-2" style="color: ${iconColor}; font-size: 1.1rem;"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.getElementById('notification-container').appendChild(notification);

    // Auto remove after duration
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, duration);
}

// Update cart count in header
function updateCartCount(count) {
    // Find the cart link and its badge
    const cartLink = document.querySelector('a[href="cart.php"]');
    if (cartLink) {
        let cartBadge = cartLink.querySelector('.cart-count');

        if (count > 0) {
            if (!cartBadge) {
                // Create badge if it doesn't exist
                cartBadge = document.createElement('span');
                cartBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count';
                cartLink.appendChild(cartBadge);
            }
            cartBadge.textContent = count;
            cartBadge.style.animation = 'pulse 0.5s ease';
        } else if (cartBadge) {
            // Remove badge if count is 0
            cartBadge.remove();
        }
    }
}

// Add to cart function
function addToCartNow(productId) {
    console.log('🛒 Adding product to cart:', productId);

    if (!productId) {
        showNotification('Invalid product ID', 'danger');
        return;
    }

    // Show loading notification
    const loadingNotification = document.createElement('div');
    loadingNotification.className = 'alert alert-info notification-toast';
    loadingNotification.style.cssText = `
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-radius: 8px;
    `;
    loadingNotification.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            <div>Adding to cart...</div>
        </div>
    `;

    createNotificationContainer();
    document.getElementById('notification-container').appendChild(loadingNotification);

    // Disable the button temporarily
    const button = document.querySelector(`button[data-product-id="${productId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
    }

    // Send AJAX request
    fetch('ajax/test_add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}&quantity=1`
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading notification
        if (loadingNotification.parentElement) {
            loadingNotification.remove();
        }

        if (data.success) {
            showNotification(
                `<strong>${data.data.product_name || 'Product'}</strong> added to cart!<br>
                <small>Cart now has ${data.data.cart_count} items</small>`,
                'success'
            );

            // Update cart count
            updateCartCount(data.data.cart_count);

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Add to Cart';
            }
        } else {
            showNotification(data.message || 'Failed to add product to cart', 'danger');

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Add to Cart';
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);

        // Remove loading notification
        if (loadingNotification.parentElement) {
            loadingNotification.remove();
        }

        showNotification('Network error. Please try again.', 'danger');

        // Reset button
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Add to Cart';
        }
    });
}

// Make function global
window.addToCartNow = addToCartNow;

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .notification-toast {
        animation: slideInRight 0.3s ease;
    }
`;
document.head.appendChild(style);

console.log('✅ Add to cart function ready!');
console.log('✅ Function type:', typeof addToCartNow);
console.log('✅ Window function type:', typeof window.addToCartNow);
