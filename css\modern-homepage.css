/* TeWuNeed Modern Homepage Styles - Blue Theme */

/* Brand Identity Colors - Blue Theme */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --white: #ffffff;

    /* Blue Gradients */
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --secondary-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --hero-gradient: linear-gradient(135deg, rgba(37, 99, 235, 0.95) 0%, rgba(29, 78, 216, 0.95) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
    --shadow-xl: 0 20px 60px rgba(0,0,0,0.2);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 25px;
}

/* Global Styles */
body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

/* Hero Section */
.hero-section {
    background: var(--hero-gradient),
                url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--white);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white"><path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"/></svg>');
    background-size: cover;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.brand-highlight {
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 400;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    max-width: 500px;
}

.hero-search {
    max-width: 500px;
}

.search-input {
    border: none;
    border-radius: var(--radius-xl);
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: var(--shadow-lg);
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

.search-btn {
    background: var(--accent-color);
    border: none;
    border-radius: var(--radius-lg);
    padding: 15px 25px;
    color: var(--white);
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #ff5252;
    transform: scale(1.05);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 2rem;
    font-weight: 800;
    display: block;
    color: var(--white);
}

.hero-stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    color: var(--white);
}

.hero-image img {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

/* Section Headers */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* Category Cards */
.categories-section {
    padding: 80px 0;
    background: var(--white);
}

.category-card {
    display: block;
    text-decoration: none;
    color: var(--dark-color);
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 30px 20px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    color: var(--dark-color);
    text-decoration: none;
}

.category-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-name {
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

/* Product Cards */
.featured-products-section {
    padding: 80px 0;
}

.product-card {
    background: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    height: 220px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: var(--radius-xl);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-wishlist {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-color);
    transition: all 0.3s ease;
    z-index: 2;
}

.product-wishlist:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.product-info {
    padding: 20px;
}

.product-category {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.rating-stars {
    color: var(--warning-color);
    font-size: 0.9rem;
}

.rating-text {
    color: #6c757d;
    font-size: 0.85rem;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 15px;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-md);
    padding: 10px 15px;
    font-weight: 600;
    color: var(--white);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-quick-view {
    background: var(--white);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    padding: 10px 12px;
    transition: all 0.3s ease;
}

.btn-quick-view:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.feature-card {
    padding: 40px 20px;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* Newsletter Section */
.newsletter-section {
    background: var(--primary-gradient) !important;
}

.newsletter-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.newsletter-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.newsletter-form .form-control {
    border: none;
    border-radius: var(--radius-lg);
    padding: 15px 20px;
    font-size: 1rem;
}

.newsletter-form .btn {
    border-radius: var(--radius-lg);
    padding: 15px 25px;
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        gap: 1rem;
    }
    
    .hero-stat-number {
        font-size: 1.5rem;
    }
}

/* Loading Animation */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
