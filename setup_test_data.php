<?php
require_once 'includes/db_connect.php';

echo "<h1>Setup Test Data</h1>";

try {
    // Check if products exist
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $productCount = $stmt->fetchColumn();
    
    echo "<p>Current products in database: $productCount</p>";
    
    if ($productCount < 5) {
        echo "<h2>Creating test products...</h2>";
        
        $testProducts = [
            [
                'product_id' => 29,
                'name' => 'Foundation Natural Glow',
                'description' => 'Natural foundation for glowing skin',
                'price' => 150000,
                'stock' => 50,
                'category_id' => 1,
                'image' => 'foundation.jpg'
            ],
            [
                'product_id' => 30,
                'name' => 'Vitamin C Serum',
                'description' => 'Brightening vitamin C serum',
                'price' => 200000,
                'stock' => 30,
                'category_id' => 2,
                'image' => 'serum.jpg'
            ],
            [
                'product_id' => 31,
                'name' => 'Organic Milk',
                'description' => 'Fresh organic milk',
                'price' => 25000,
                'stock' => 100,
                'category_id' => 3,
                'image' => 'milk.jpg'
            ],
            [
                'product_id' => 32,
                'name' => 'Running Shoes',
                'description' => 'Comfortable running shoes',
                'price' => 500000,
                'stock' => 20,
                'category_id' => 4,
                'image' => 'shoes.jpg'
            ],
            [
                'product_id' => 33,
                'name' => 'Fresh Carrots',
                'description' => 'Organic fresh carrots',
                'price' => 15000,
                'stock' => 80,
                'category_id' => 5,
                'image' => 'carrots.jpg'
            ]
        ];
        
        foreach ($testProducts as $product) {
            try {
                // Check if product already exists
                $checkStmt = $conn->prepare("SELECT product_id FROM products WHERE product_id = ?");
                $checkStmt->execute([$product['product_id']]);
                
                if (!$checkStmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO products (product_id, name, description, price, stock, category_id, image, is_active, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
                    ");
                    $stmt->execute([
                        $product['product_id'],
                        $product['name'],
                        $product['description'],
                        $product['price'],
                        $product['stock'],
                        $product['category_id'],
                        $product['image']
                    ]);
                    echo "<p style='color: green;'>✅ Created product: {$product['name']} (ID: {$product['product_id']})</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Product already exists: {$product['name']} (ID: {$product['product_id']})</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error creating product {$product['name']}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Check if categories exist
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM categories");
    $stmt->execute();
    $categoryCount = $stmt->fetchColumn();
    
    echo "<p>Current categories in database: $categoryCount</p>";
    
    if ($categoryCount < 5) {
        echo "<h2>Creating test categories...</h2>";
        
        $testCategories = [
            ['category_id' => 1, 'name' => 'Cosmetics', 'slug' => 'cosmetics', 'description' => 'Beauty and cosmetic products'],
            ['category_id' => 2, 'name' => 'Medicine', 'slug' => 'medicine', 'description' => 'Health and medical products'],
            ['category_id' => 3, 'name' => 'Milk Products', 'slug' => 'milk-products', 'description' => 'Dairy and milk products'],
            ['category_id' => 4, 'name' => 'Sports', 'slug' => 'sports', 'description' => 'Sports and fitness equipment'],
            ['category_id' => 5, 'name' => 'Vegetables', 'slug' => 'vegetables', 'description' => 'Fresh vegetables and produce']
        ];
        
        foreach ($testCategories as $category) {
            try {
                // Check if category already exists
                $checkStmt = $conn->prepare("SELECT category_id FROM categories WHERE category_id = ?");
                $checkStmt->execute([$category['category_id']]);
                
                if (!$checkStmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO categories (category_id, name, slug, description, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, NOW(), NOW())
                    ");
                    $stmt->execute([
                        $category['category_id'],
                        $category['name'],
                        $category['slug'],
                        $category['description']
                    ]);
                    echo "<p style='color: green;'>✅ Created category: {$category['name']} (ID: {$category['category_id']})</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Category already exists: {$category['name']} (ID: {$category['category_id']})</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error creating category {$category['name']}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Check if test user exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = 1");
    $stmt->execute();
    $userExists = $stmt->fetch();
    
    if (!$userExists) {
        echo "<h2>Creating test user...</h2>";
        try {
            $stmt = $conn->prepare("
                INSERT INTO users (user_id, name, email, password, role, created_at, updated_at) 
                VALUES (1, 'Test User', '<EMAIL>', ?, 'customer', NOW(), NOW())
            ");
            $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
            echo "<p style='color: green;'>✅ Created test user (ID: 1, Email: <EMAIL>, Password: password)</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error creating test user: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Test user already exists (ID: 1)</p>";
    }
    
    echo "<h2>Database Setup Complete!</h2>";
    echo "<p><a href='test_ajax.php'>Test AJAX Add to Cart</a></p>";
    echo "<p><a href='products_public.php'>View Products Page</a></p>";
    echo "<p><a href='cart.php'>View Cart Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    background: #e9ecef;
    border-radius: 3px;
    margin: 5px;
    display: inline-block;
}

a:hover {
    background: #007bff;
    color: white;
}
</style>
