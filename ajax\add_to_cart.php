<?php
// Optimized cart addition with minimal overhead
session_start();
require_once '../includes/db_connect.php';

// Set response headers for performance
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Quick validation
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Login required']);
    exit;
}

if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid product ID']);
    exit;
}

$product_id = (int)$_POST['product_id'];
$quantity = isset($_POST['quantity']) && is_numeric($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
$user_id = $_SESSION['user_id'];

if ($quantity <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid quantity']);
    exit;
}

try {
    // Optimized single query to get product and check stock
    $stmt = $conn->prepare("
        SELECT product_id, name, price, stock
        FROM products
        WHERE product_id = ? AND is_active = 1 AND stock >= ?
    ");
    $stmt->execute([$product_id, $quantity]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        // Check if product exists but out of stock
        $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ? AND is_active = 1");
        $stmt->execute([$product_id]);
        $stock_check = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($stock_check) {
            throw new Exception("Insufficient stock. Available: {$stock_check['stock']}");
        } else {
            throw new Exception('Product not available');
        }
    }

    // Get or create cart efficiently
    $stmt = $conn->prepare("
        SELECT cart_id FROM carts WHERE user_id = ?
        UNION ALL
        SELECT LAST_INSERT_ID() FROM (
            INSERT IGNORE INTO carts (user_id, created_at) VALUES (?, NOW())
        ) AS new_cart
        LIMIT 1
    ");
    $stmt->execute([$user_id, $user_id]);
    $cart_id = $stmt->fetchColumn();

    if (!$cart_id) {
        // Fallback: get existing cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart_id = $stmt->fetchColumn();
    }

    // Use optimized UPSERT for cart items
    $stmt = $conn->prepare("
        INSERT INTO cart_items (cart_id, product_id, quantity, added_at)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
            quantity = quantity + VALUES(quantity),
            added_at = NOW()
    ");
    $stmt->execute([$cart_id, $product_id, $quantity]);

    // Get updated cart count in single query
    $stmt = $conn->prepare("
        SELECT
            SUM(ci.quantity) as cart_count,
            ci.quantity as item_quantity
        FROM cart_items ci
        WHERE ci.cart_id = ? AND ci.product_id = ?
        GROUP BY ci.cart_id
    ");
    $stmt->execute([$cart_id, $product_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $cart_count = $result['cart_count'] ?? 0;
    $item_quantity = $result['item_quantity'] ?? $quantity;

    // Success response
    echo json_encode([
        'success' => true,
        'message' => $product['name'] . ' added to cart',
        'cart_count' => $cart_count,
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity' => $quantity,
        'item_quantity' => $item_quantity,
        'product_price' => $product['price'],
        'cart_total' => $cart_count * $product['price'] // Simplified calculation
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
