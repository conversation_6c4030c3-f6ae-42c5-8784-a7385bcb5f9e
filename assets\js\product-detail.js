document.addEventListener('DOMContentLoaded', () => {
    // Get elements
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const alertContainer = document.getElementById('alert-container');
    const spinnerElement = document.getElementById('spinner');

    // Helper functions
    function showSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
    }

    function hideSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
    }

    function showAlert(message, type = 'success') {
        if (!alertContainer) return;

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        alertContainer.innerHTML = alertHtml;

        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    if (quantityInput && decreaseBtn && increaseBtn) {
        // Update quantity
        decreaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });

        increaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (value < max) {
                quantityInput.value = value + 1;
            }
        });

        // Validate quantity input
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (isNaN(value) || value < 1) {
                quantityInput.value = 1;
            } else if (value > max) {
                quantityInput.value = max;
            }
        });
    }

    // Initialize simple cart for product detail page
    if (typeof SimpleCart !== 'undefined') {
        const cart = new SimpleCart();

        // Override the default add to cart behavior for product detail
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const productId = this.dataset.productId;
                const quantity = parseInt(quantityInput?.value || 1);

                // Disable button while processing
                this.disabled = true;
                const oldText = this.innerHTML;
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adding...';

                showSpinner();

                // Use SimpleCart to add item
                cart.addToCart(productId, quantity)
                    .then(response => {
                        hideSpinner();

                        if (response.success) {
                            showAlert(response.message, 'success');

                            // Change button text temporarily
                            this.innerHTML = '<i class="fas fa-check me-2"></i>Added!';
                            setTimeout(() => {
                                this.disabled = false;
                                this.innerHTML = oldText;
                            }, 2000);
                        } else {
                            showAlert(response.message, 'danger');
                            this.disabled = false;
                            this.innerHTML = oldText;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        hideSpinner();
                        this.disabled = false;
                        this.innerHTML = oldText;
                        showAlert('Error adding product to cart. Please try again.', 'danger');
                    });
            });
        }
    }
});
