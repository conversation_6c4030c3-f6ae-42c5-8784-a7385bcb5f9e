/**
 * Client-side Notification Reset Functions
 * Provides comprehensive notification clearing capabilities
 */

class NotificationReset {
    constructor() {
        this.init();
    }

    init() {
        // Add reset button to notification container if it exists
        this.addResetButton();
        
        // Listen for reset events
        this.setupEventListeners();
    }

    /**
     * Add a reset button to the notification container
     */
    addResetButton() {
        const container = document.getElementById('notification-container');
        if (container && !document.getElementById('notification-reset-btn')) {
            const resetBtn = document.createElement('button');
            resetBtn.id = 'notification-reset-btn';
            resetBtn.className = 'btn btn-sm btn-outline-secondary notification-reset-btn';
            resetBtn.innerHTML = '<i class="fas fa-times"></i> Clear All';
            resetBtn.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                display: none;
                font-size: 12px;
                padding: 5px 10px;
            `;
            
            resetBtn.addEventListener('click', () => this.clearAllNotifications());
            document.body.appendChild(resetBtn);
            
            // Show button when notifications are present
            this.toggleResetButton();
        }
    }

    /**
     * Setup event listeners for notification management
     */
    setupEventListeners() {
        // Listen for new notifications to show/hide reset button
        const observer = new MutationObserver(() => {
            this.toggleResetButton();
        });

        const container = document.getElementById('notification-container');
        if (container) {
            observer.observe(container, { childList: true, subtree: true });
        }

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+C to clear notifications
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.clearAllNotifications();
            }
        });
    }

    /**
     * Toggle visibility of reset button based on notification presence
     */
    toggleResetButton() {
        const resetBtn = document.getElementById('notification-reset-btn');
        const container = document.getElementById('notification-container');
        
        if (resetBtn && container) {
            const hasNotifications = container.children.length > 0;
            resetBtn.style.display = hasNotifications ? 'block' : 'none';
        }
    }

    /**
     * Clear all client-side notifications
     */
    clearAllNotifications() {
        try {
            // Clear notification manager queue and active notifications
            if (window.notificationManager) {
                window.notificationManager.clearAll();
            }

            // Clear notification container
            const container = document.getElementById('notification-container');
            if (container) {
                container.innerHTML = '';
            }

            // Clear any Toastify notifications
            if (typeof Toastify !== 'undefined') {
                // Remove all existing toasts
                const toasts = document.querySelectorAll('.toastify');
                toasts.forEach(toast => toast.remove());
            }

            // Clear any Bootstrap alerts
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                alert.remove();
            });

            // Clear any custom notification elements
            const customNotifications = document.querySelectorAll('.notification, .toast, .alert-toast');
            customNotifications.forEach(notification => {
                notification.remove();
            });

            // Hide reset button
            this.toggleResetButton();

            console.log('All client-side notifications cleared');
            
            // Optional: Show confirmation
            this.showClearConfirmation();

        } catch (error) {
            console.error('Error clearing notifications:', error);
        }
    }

    /**
     * Clear specific type of notifications
     */
    clearNotificationType(type) {
        const container = document.getElementById('notification-container');
        if (container) {
            const notifications = container.querySelectorAll(`[data-type="${type}"]`);
            notifications.forEach(notification => notification.remove());
        }

        // Clear from notification manager queue
        if (window.notificationManager && window.notificationManager.queue) {
            window.notificationManager.queue = window.notificationManager.queue.filter(
                item => item.type !== type
            );
        }

        this.toggleResetButton();
    }

    /**
     * Clear notifications older than specified time
     */
    clearOldNotifications(maxAge = 5000) {
        const container = document.getElementById('notification-container');
        if (container) {
            const notifications = container.children;
            const now = Date.now();

            Array.from(notifications).forEach(notification => {
                const createdAt = notification.dataset.createdAt;
                if (createdAt && (now - parseInt(createdAt)) > maxAge) {
                    notification.remove();
                }
            });
        }

        this.toggleResetButton();
    }

    /**
     * Show confirmation that notifications were cleared
     */
    showClearConfirmation() {
        // Create a simple confirmation message
        const confirmation = document.createElement('div');
        confirmation.className = 'alert alert-success alert-dismissible fade show';
        confirmation.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 250px;
        `;
        confirmation.innerHTML = `
            <i class="fas fa-check-circle"></i> All notifications cleared
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(confirmation);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (confirmation.parentNode) {
                confirmation.remove();
            }
        }, 3000);
    }

    /**
     * Reset all notification-related data (comprehensive reset)
     */
    async comprehensiveReset() {
        try {
            // Clear client-side notifications
            this.clearAllNotifications();

            // Clear server-side notifications via API
            const response = await fetch('clear_notifications.php?type=all&format=json');
            const result = await response.json();

            if (result.success) {
                console.log('Server-side notifications cleared:', result.cleared);
                
                // Show success message
                if (window.notificationManager) {
                    window.notificationManager.addNotification(
                        'All notifications have been reset successfully',
                        'success',
                        { duration: 3000 }
                    );
                }
            } else {
                console.error('Failed to clear server-side notifications:', result.message);
            }

        } catch (error) {
            console.error('Error during comprehensive reset:', error);
        }
    }

    /**
     * Get notification statistics
     */
    getNotificationStats() {
        const container = document.getElementById('notification-container');
        const stats = {
            total: 0,
            byType: {},
            unread: 0
        };

        if (container) {
            const notifications = container.children;
            stats.total = notifications.length;

            Array.from(notifications).forEach(notification => {
                const type = notification.dataset.type || 'unknown';
                stats.byType[type] = (stats.byType[type] || 0) + 1;

                if (!notification.classList.contains('read')) {
                    stats.unread++;
                }
            });
        }

        // Add queue stats if available
        if (window.notificationManager && window.notificationManager.queue) {
            stats.queued = window.notificationManager.queue.length;
        }

        return stats;
    }
}

// Global functions for easy access
window.NotificationReset = NotificationReset;

// Utility functions
window.clearAllNotifications = function() {
    if (window.notificationResetInstance) {
        window.notificationResetInstance.clearAllNotifications();
    }
};

window.clearNotificationType = function(type) {
    if (window.notificationResetInstance) {
        window.notificationResetInstance.clearNotificationType(type);
    }
};

window.comprehensiveNotificationReset = function() {
    if (window.notificationResetInstance) {
        window.notificationResetInstance.comprehensiveReset();
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.notificationResetInstance = new NotificationReset();
    
    // Add global keyboard shortcut info to console
    console.log('Notification Reset loaded. Use Ctrl+Shift+C to clear all notifications.');
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationReset;
}
