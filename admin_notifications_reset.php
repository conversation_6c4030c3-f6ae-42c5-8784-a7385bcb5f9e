<?php
/**
 * Admin Notification Management Interface
 */

session_start();
require_once 'includes/config.php';

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

$page_title = 'Notification Management';

// Handle reset actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $target = $_POST['target'] ?? 'all';
    
    try {
        switch ($action) {
            case 'clear_all_user_notifications':
                $stmt = $conn->prepare("DELETE FROM order_notifications");
                $stmt->execute();
                $cleared = $stmt->rowCount();
                $_SESSION['success_message'] = "Cleared {$cleared} user notifications";
                break;
                
            case 'clear_admin_notifications':
                $stmt = $conn->prepare("DELETE FROM admin_notifications");
                $stmt->execute();
                $cleared = $stmt->rowCount();
                $_SESSION['success_message'] = "Cleared {$cleared} admin notifications";
                break;
                
            case 'mark_all_read':
                $stmt1 = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE is_read = FALSE");
                $stmt1->execute();
                $marked1 = $stmt1->rowCount();
                
                $stmt2 = $conn->prepare("UPDATE admin_notifications SET is_read = TRUE WHERE is_read = FALSE");
                $stmt2->execute();
                $marked2 = $stmt2->rowCount();
                
                $_SESSION['success_message'] = "Marked " . ($marked1 + $marked2) . " notifications as read";
                break;
                
            case 'clear_specific_user':
                $user_id = $_POST['user_id'] ?? 0;
                if ($user_id > 0) {
                    $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    $cleared = $stmt->rowCount();
                    $_SESSION['success_message'] = "Cleared {$cleared} notifications for user ID {$user_id}";
                }
                break;
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = "Error: " . $e->getMessage();
    }
    
    header('Location: admin_notifications_reset.php');
    exit;
}

// Get notification statistics
$stats = [];
try {
    // User notifications
    $stmt = $conn->query("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread FROM order_notifications");
    $stats['user'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Admin notifications
    $stmt = $conn->query("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread FROM admin_notifications");
    $stats['admin'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Recent notifications
    $stmt = $conn->query("
        SELECT 'user' as type, user_id, title, message, created_at, is_read 
        FROM order_notifications 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stats['recent'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $stats = ['error' => $e->getMessage()];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Notification Management</span>
                    </h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#overview">
                                <i class="fas fa-chart-bar"></i> Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#actions">
                                <i class="fas fa-tools"></i> Reset Actions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#recent">
                                <i class="fas fa-clock"></i> Recent Notifications
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Notification Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="admin_dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                <!-- Display messages -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Overview Section -->
                <section id="overview" class="mb-5">
                    <h3>Notification Statistics</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">User Notifications</h5>
                                    <p class="card-text">
                                        <strong>Total:</strong> <?php echo $stats['user']['total'] ?? 0; ?><br>
                                        <strong>Unread:</strong> <?php echo $stats['user']['unread'] ?? 0; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Admin Notifications</h5>
                                    <p class="card-text">
                                        <strong>Total:</strong> <?php echo $stats['admin']['total'] ?? 0; ?><br>
                                        <strong>Unread:</strong> <?php echo $stats['admin']['unread'] ?? 0; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Reset Actions Section -->
                <section id="actions" class="mb-5">
                    <h3>Reset Actions</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Clear All Notifications</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" onsubmit="return confirm('Are you sure you want to clear ALL user notifications? This cannot be undone.')">
                                        <input type="hidden" name="action" value="clear_all_user_notifications">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> Clear All User Notifications
                                        </button>
                                    </form>
                                    <hr>
                                    <form method="POST" onsubmit="return confirm('Are you sure you want to clear ALL admin notifications? This cannot be undone.')">
                                        <input type="hidden" name="action" value="clear_admin_notifications">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-trash"></i> Clear All Admin Notifications
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Mark as Read</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="mark_all_read">
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-check"></i> Mark All as Read
                                        </button>
                                    </form>
                                    <hr>
                                    <form method="POST">
                                        <div class="mb-3">
                                            <label for="user_id" class="form-label">Clear for Specific User ID:</label>
                                            <input type="number" class="form-control" id="user_id" name="user_id" required>
                                        </div>
                                        <input type="hidden" name="action" value="clear_specific_user">
                                        <button type="submit" class="btn btn-secondary">
                                            <i class="fas fa-user-times"></i> Clear User Notifications
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Recent Notifications Section -->
                <section id="recent" class="mb-5">
                    <h3>Recent Notifications</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>User ID</th>
                                    <th>Title</th>
                                    <th>Message</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($stats['recent']) && count($stats['recent']) > 0): ?>
                                    <?php foreach ($stats['recent'] as $notification): ?>
                                        <tr>
                                            <td><span class="badge bg-primary"><?php echo $notification['type']; ?></span></td>
                                            <td><?php echo $notification['user_id']; ?></td>
                                            <td><?php echo htmlspecialchars($notification['title']); ?></td>
                                            <td><?php echo htmlspecialchars(substr($notification['message'], 0, 50)) . '...'; ?></td>
                                            <td><?php echo date('M j, Y H:i', strtotime($notification['created_at'])); ?></td>
                                            <td>
                                                <?php if ($notification['is_read']): ?>
                                                    <span class="badge bg-success">Read</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Unread</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No recent notifications found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Quick Links -->
                <section class="mb-5">
                    <h3>Quick Links</h3>
                    <div class="d-flex gap-2">
                        <a href="clear_notifications.php?type=all&format=json" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-code"></i> Test API (JSON)
                        </a>
                        <a href="clear_notifications.php?type=session" class="btn btn-outline-secondary">
                            <i class="fas fa-broom"></i> Clear Session Only
                        </a>
                        <a href="clear_notifications.php?type=cookies" class="btn btn-outline-info">
                            <i class="fas fa-cookie-bite"></i> Clear Cookies Only
                        </a>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
