<?php
/**
 * Quick fix to clear the header notification badge
 * This will clear the notifications causing the "4" badge to appear
 */

session_start();
require_once 'includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];
$response = ['success' => false, 'message' => '', 'cleared' => 0];

try {
    // Get current unread notifications
    $stmt = $conn->prepare("
        SELECT notification_id, title, message, created_at 
        FROM order_notifications 
        WHERE user_id = ? AND is_read = FALSE
    ");
    $stmt->execute([$user_id]);
    $unread_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response['found_notifications'] = $unread_notifications;
    $response['count_before'] = count($unread_notifications);
    
    // Option 1: Mark all as read (recommended)
    if (isset($_GET['action']) && $_GET['action'] === 'mark_read') {
        $stmt = $conn->prepare("
            UPDATE order_notifications 
            SET is_read = TRUE 
            WHERE user_id = ? AND is_read = FALSE
        ");
        $stmt->execute([$user_id]);
        $response['cleared'] = $stmt->rowCount();
        $response['action'] = 'marked_as_read';
        $response['message'] = "Marked {$response['cleared']} notifications as read";
    }
    
    // Option 2: Delete all notifications (if requested)
    elseif (isset($_GET['action']) && $_GET['action'] === 'delete') {
        $stmt = $conn->prepare("
            DELETE FROM order_notifications 
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $response['cleared'] = $stmt->rowCount();
        $response['action'] = 'deleted';
        $response['message'] = "Deleted {$response['cleared']} notifications";
    }
    
    // Default: Just show what would be cleared
    else {
        $response['message'] = "Found {$response['count_before']} unread notifications. Add ?action=mark_read or ?action=delete to clear them.";
    }
    
    // Get updated count
    $stmt = $conn->prepare("
        SELECT COUNT(*) as unread_count
        FROM order_notifications
        WHERE user_id = ? AND is_read = FALSE
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $response['count_after'] = $result['unread_count'] ?? 0;
    
    $response['success'] = true;
    
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log("Clear header notifications error: " . $e->getMessage());
}

// Handle different response formats
if (isset($_GET['format']) && $_GET['format'] === 'json') {
    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

// HTML response for browser viewing
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Header Notifications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bell-slash"></i>
                            Clear Header Notification Badge
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($response['success']): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $response['message']; ?>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-primary"><?php echo $response['count_before']; ?></h3>
                                            <small class="text-muted">Notifications Found</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="text-success"><?php echo $response['count_after']; ?></h3>
                                            <small class="text-muted">Unread After Action</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (isset($response['found_notifications']) && count($response['found_notifications']) > 0): ?>
                                <h6 class="mt-4">Found Notifications:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Title</th>
                                                <th>Message</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($response['found_notifications'] as $notification): ?>
                                                <tr>
                                                    <td><?php echo $notification['notification_id']; ?></td>
                                                    <td><?php echo htmlspecialchars($notification['title']); ?></td>
                                                    <td><?php echo htmlspecialchars(substr($notification['message'], 0, 50)) . '...'; ?></td>
                                                    <td><?php echo date('M j, H:i', strtotime($notification['created_at'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                            
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i>
                                <?php echo $response['message']; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h6>Quick Actions:</h6>
                            <div class="btn-group" role="group">
                                <a href="clear_header_notifications.php?action=mark_read" class="btn btn-success">
                                    <i class="fas fa-check"></i> Mark All as Read
                                </a>
                                <a href="clear_header_notifications.php?action=delete" class="btn btn-danger" 
                                   onclick="return confirm('Are you sure you want to delete all notifications? This cannot be undone.')">
                                    <i class="fas fa-trash"></i> Delete All
                                </a>
                                <a href="clear_header_notifications.php?format=json" class="btn btn-info" target="_blank">
                                    <i class="fas fa-code"></i> View JSON
                                </a>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> Back to Home
                            </a>
                            <a href="user_notifications.php" class="btn btn-outline-primary">
                                <i class="fas fa-bell"></i> View Notifications
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-refresh header notification count after action
        <?php if (isset($_GET['action']) && $response['success']): ?>
        setTimeout(function() {
            // Try to update header notification count
            if (typeof updateHeaderNotificationCount === 'function') {
                updateHeaderNotificationCount();
            }
            
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-info alert-dismissible fade show mt-3';
            alert.innerHTML = `
                <i class="fas fa-info-circle"></i>
                The header notification badge should now be updated. If it still shows, try refreshing the page.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.card-body').appendChild(alert);
        }, 1000);
        <?php endif; ?>
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
