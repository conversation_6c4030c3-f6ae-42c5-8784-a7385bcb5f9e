/**
 * Simple Cart JavaScript - Lightweight and Reliable
 * For TeWuNeed website
 */

console.log('🛒 Simple Cart JS loaded');

// Simple cart functionality
function simpleAddToCart(productId, button) {
    console.log('Adding product to cart:', productId);
    
    // Prevent double clicks
    if (button.disabled) {
        console.log('Button already disabled, ignoring click');
        return;
    }
    
    // Set loading state
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    
    // Create form data
    const formData = new FormData();
    formData.append('product_id', productId);
    formData.append('quantity', 1);
    
    // Send request
    fetch('ajax/add_to_cart_simple.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Server response:', data);
        
        if (data.success) {
            // Success
            button.innerHTML = '<i class="fas fa-check"></i> Added!';
            button.classList.add('btn-success');
            
            // Update cart count
            updateCartCount(data.cart_count);
            
            // Show notification
            showSimpleNotification(data.message || 'Product added to cart!', 'success');
            
            // Reset button after 2 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.disabled = false;
            }, 2000);
            
        } else {
            // Error from server
            throw new Error(data.message || 'Failed to add to cart');
        }
    })
    .catch(error => {
        console.error('Add to cart error:', error);
        
        // Show error
        showSimpleNotification('Error: ' + error.message, 'error');
        
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Update cart count in UI
function updateCartCount(count) {
    console.log('Updating cart count to:', count);
    
    const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
    cartElements.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.style.display = 'inline';
            element.classList.add('animate-bounce');
            setTimeout(() => element.classList.remove('animate-bounce'), 600);
        } else {
            element.style.display = 'none';
        }
    });
}

// Simple notification system
function showSimpleNotification(message, type = 'info') {
    console.log('Showing notification:', message, type);
    
    // Remove existing notifications
    document.querySelectorAll('.simple-notification').forEach(n => n.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} simple-notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-radius: 8px;
    `;
    
    const icon = type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-circle' : 'info-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Simple cart initialized');
    
    // Add click handlers to all add to cart buttons
    document.addEventListener('click', function(e) {
        // Handle add to cart buttons
        if (e.target.matches('.add-to-cart-btn, .btn-add-to-cart') || 
            e.target.closest('.add-to-cart-btn, .btn-add-to-cart')) {
            
            e.preventDefault();
            
            const button = e.target.matches('.add-to-cart-btn, .btn-add-to-cart') ? 
                          e.target : e.target.closest('.add-to-cart-btn, .btn-add-to-cart');
            
            // Get product ID
            const productId = button.dataset.productId || 
                             button.getAttribute('data-product-id') ||
                             button.closest('[data-product-id]')?.dataset.productId;
            
            if (productId) {
                console.log('Add to cart clicked for product:', productId);
                simpleAddToCart(productId, button);
            } else {
                console.error('Product ID not found');
                showSimpleNotification('Product ID not found', 'error');
            }
        }
    });
    
    // Load initial cart count
    loadCartCount();
});

// Load cart count from server
function loadCartCount() {
    fetch('ajax/get_cart_count.php', {
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success !== undefined) {
            updateCartCount(data.cart_count || 0);
        } else {
            // Handle old format
            updateCartCount(data.count || 0);
        }
    })
    .catch(error => {
        console.log('Could not load cart count:', error);
    });
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-bounce {
        animation: bounce 0.6s ease;
    }
    
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        80% { transform: translateY(-5px); }
    }
    
    .btn-success {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        color: white !important;
    }
    
    .simple-notification {
        pointer-events: auto;
    }
`;
document.head.appendChild(style);

// Global functions for backward compatibility
window.simpleAddToCart = simpleAddToCart;
window.updateCartCount = updateCartCount;
window.showSimpleNotification = showSimpleNotification;

console.log('✅ Simple cart functionality ready');
