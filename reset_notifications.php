<?php
/**
 * Simple User Notification Reset Page
 * Provides a user-friendly interface to reset notifications
 */

session_start();
require_once 'includes/config.php';

$page_title = 'Reset Notifications';
$user_id = $_SESSION['user_id'] ?? null;

// Handle reset request
if ($_POST && isset($_POST['reset_type'])) {
    $reset_type = $_POST['reset_type'];
    $success = false;
    $message = '';
    
    try {
        switch ($reset_type) {
            case 'clear_all':
                // Redirect to comprehensive clear
                header('Location: clear_notifications.php?type=all&redirect=' . urlencode($_SERVER['REQUEST_URI']));
                exit;
                
            case 'mark_read':
                if ($user_id) {
                    // Mark all as read instead of deleting
                    $stmt = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE user_id = ? AND is_read = FALSE");
                    $stmt->execute([$user_id]);
                    $marked = $stmt->rowCount();
                    
                    $success = true;
                    $message = "Marked {$marked} notifications as read.";
                } else {
                    $message = "Please log in to mark notifications as read.";
                }
                break;
                
            case 'clear_session':
                // Clear only session notifications
                header('Location: clear_notifications.php?type=session&redirect=' . urlencode($_SERVER['REQUEST_URI']));
                exit;
                
            default:
                $message = "Invalid reset type.";
        }
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
    }
    
    if ($success) {
        $_SESSION['success_message'] = $message;
    } else {
        $_SESSION['error_message'] = $message;
    }
    
    header('Location: reset_notifications.php');
    exit;
}

// Get current notification count
$notification_count = 0;
$unread_count = 0;

if ($user_id) {
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread FROM order_notifications WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $notification_count = $result['total'] ?? 0;
        $unread_count = $result['unread'] ?? 0;
    } catch (Exception $e) {
        // Table might not exist
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .notification-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .reset-btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-6 mb-3">
                        <i class="fas fa-bell-slash text-primary"></i>
                        Reset Notifications
                    </h1>
                    <p class="text-muted">Manage and clear your notifications</p>
                </div>

                <!-- Display messages -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Notification Statistics -->
                <?php if ($user_id): ?>
                    <div class="card stat-card mb-4">
                        <div class="card-body text-center">
                            <div class="row">
                                <div class="col-6">
                                    <h3 class="mb-0"><?php echo $notification_count; ?></h3>
                                    <small>Total Notifications</small>
                                </div>
                                <div class="col-6">
                                    <h3 class="mb-0"><?php echo $unread_count; ?></h3>
                                    <small>Unread Notifications</small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Reset Options -->
                <div class="row">
                    <!-- Mark as Read -->
                    <div class="col-md-6 mb-4">
                        <div class="card notification-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-check-circle fa-3x text-success"></i>
                                </div>
                                <h5 class="card-title">Mark All as Read</h5>
                                <p class="card-text text-muted">
                                    Keep notifications but mark them as read. This won't delete your notification history.
                                </p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="reset_type" value="mark_read">
                                    <button type="submit" class="btn btn-success reset-btn" <?php echo !$user_id ? 'disabled' : ''; ?>>
                                        <i class="fas fa-check"></i> Mark as Read
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Session -->
                    <div class="col-md-6 mb-4">
                        <div class="card notification-card h-100">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-broom fa-3x text-warning"></i>
                                </div>
                                <h5 class="card-title">Clear Session</h5>
                                <p class="card-text text-muted">
                                    Clear temporary notifications and error messages from your current session.
                                </p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="reset_type" value="clear_session">
                                    <button type="submit" class="btn btn-warning reset-btn">
                                        <i class="fas fa-broom"></i> Clear Session
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Clear All -->
                    <div class="col-12 mb-4">
                        <div class="card notification-card border-danger">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-trash-alt fa-3x text-danger"></i>
                                </div>
                                <h5 class="card-title text-danger">Clear All Notifications</h5>
                                <p class="card-text text-muted">
                                    <strong>Warning:</strong> This will permanently delete all your notifications, 
                                    including order updates and system messages. This action cannot be undone.
                                </p>
                                <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to permanently delete ALL notifications? This action cannot be undone.')">
                                    <input type="hidden" name="reset_type" value="clear_all">
                                    <button type="submit" class="btn btn-danger reset-btn" <?php echo !$user_id ? 'disabled' : ''; ?>>
                                        <i class="fas fa-trash-alt"></i> Clear All Notifications
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Options -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cog"></i> Additional Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearClientSideNotifications()">
                                    <i class="fas fa-desktop"></i> Clear Page Notifications
                                </button>
                                <small class="text-muted d-block">Clear notifications currently visible on this page</small>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.location.reload()">
                                    <i class="fas fa-sync-alt"></i> Refresh Page
                                </button>
                                <small class="text-muted d-block">Reload the page to see current notification status</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="text-center">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                    <?php if ($user_id): ?>
                        <a href="user_notifications.php" class="btn btn-outline-primary">
                            <i class="fas fa-bell"></i> View Notifications
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/notification-reset.js"></script>
    <script>
        function clearClientSideNotifications() {
            if (typeof clearAllNotifications === 'function') {
                clearAllNotifications();
                
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show mt-3';
                alert.innerHTML = `
                    <i class="fas fa-check-circle"></i> Page notifications cleared successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                const container = document.querySelector('.container');
                container.insertBefore(alert, container.firstChild);
                
                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 3000);
            } else {
                alert('Notification reset functionality not available on this page.');
            }
        }

        // Show login message if not logged in
        <?php if (!$user_id): ?>
            document.addEventListener('DOMContentLoaded', function() {
                const loginAlert = document.createElement('div');
                loginAlert.className = 'alert alert-info';
                loginAlert.innerHTML = `
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> Some features require you to be logged in. 
                    <a href="login.php" class="alert-link">Login here</a> to access all notification management features.
                `;
                
                const container = document.querySelector('.container .row .col-md-8');
                container.insertBefore(loginAlert, container.children[1]);
            });
        <?php endif; ?>
    </script>
</body>
</html>
