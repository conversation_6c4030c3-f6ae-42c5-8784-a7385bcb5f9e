<?php
session_start();

// Simple test to verify cart functionality
echo "<h1>Cart Test Page</h1>";

echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Test login as user ID 1
if (isset($_GET['login'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
    echo "<div style='color: green; font-weight: bold;'>✅ Logged in as Test User (ID: 1)</div>";
}

// Test logout
if (isset($_GET['logout'])) {
    session_destroy();
    session_start();
    echo "<div style='color: red; font-weight: bold;'>❌ Logged out</div>";
}

echo "<h2>Actions:</h2>";
if (!isset($_SESSION['user_id'])) {
    echo "<a href='?login=1' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Login as Test User</a><br><br>";
} else {
    echo "<a href='?logout=1' style='background: red; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Logout</a><br><br>";
}

echo "<a href='products_public.php' style='background: blue; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Go to Products Page</a><br><br>";
echo "<a href='cart.php' style='background: orange; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Go to Cart Page</a><br><br>";

// Test add to cart directly
if (isset($_GET['test_add'])) {
    echo "<h2>Testing Add to Cart:</h2>";
    
    // Simulate POST request
    $_POST['product_id'] = 29;
    $_POST['quantity'] = 1;
    
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
    echo "Simulating: POST product_id=29, quantity=1<br>";
    
    // Include the add to cart script
    ob_start();
    include 'ajax/test_add_to_cart.php';
    $result = ob_get_clean();
    
    echo "Result: <pre>$result</pre>";
    echo "</div>";
}

if (isset($_SESSION['user_id'])) {
    echo "<a href='?test_add=1' style='background: purple; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Test Add Product 29 to Cart</a><br><br>";
}

// Show database info
try {
    require_once 'includes/db_connect.php';
    
    echo "<h2>Database Info:</h2>";
    
    // Check if user exists
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div style='color: green;'>✅ User exists in database</div>";
            echo "<pre>";
            print_r($user);
            echo "</pre>";
        } else {
            echo "<div style='color: red;'>❌ User does not exist in database</div>";
            
            // Create test user
            if (isset($_GET['create_user'])) {
                $stmt = $conn->prepare("INSERT INTO users (user_id, name, email, password, created_at) VALUES (?, ?, ?, ?, NOW())");
                $stmt->execute([1, 'Test User', '<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
                echo "<div style='color: green;'>✅ Test user created!</div>";
            } else {
                echo "<a href='?login=1&create_user=1' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Create Test User</a><br><br>";
            }
        }
        
        // Check cart
        $stmt = $conn->prepare("SELECT * FROM carts WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $cart = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($cart) {
            echo "<div style='color: green;'>✅ Cart exists</div>";
            echo "<pre>";
            print_r($cart);
            echo "</pre>";
            
            // Check cart items
            $stmt = $conn->prepare("
                SELECT ci.*, p.name, p.price 
                FROM cart_items ci 
                LEFT JOIN products p ON ci.product_id = p.product_id 
                WHERE ci.cart_id = ?
            ");
            $stmt->execute([$cart['cart_id']]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Cart Items:</h3>";
            if ($items) {
                echo "<pre>";
                print_r($items);
                echo "</pre>";
            } else {
                echo "<div>No items in cart</div>";
            }
        } else {
            echo "<div style='color: orange;'>⚠️ No cart exists (will be created when adding items)</div>";
        }
    }
    
    // Check products
    $stmt = $conn->prepare("SELECT product_id, name, price, stock FROM products LIMIT 5");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Sample Products:</h3>";
    if ($products) {
        echo "<pre>";
        print_r($products);
        echo "</pre>";
    } else {
        echo "<div style='color: red;'>❌ No products found</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Database Error: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

a {
    display: inline-block;
    margin: 5px;
}

pre {
    background: #fff;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
