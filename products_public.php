<?php
// Start session
session_start();

// Set page variables for header
$page = 'products';
$page_title = '';

// Load konfigurasi dan fungsi
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Get all categories for filter
$categories = [];
try {
    if (isset($conn) && $conn) {
        $categoryQuery = "SELECT category_id, name, slug, description FROM categories ORDER BY name";
        $categoryStmt = $conn->prepare($categoryQuery);
        $categoryStmt->execute();
        $categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);

        // If no categories found, insert default ones
        if (empty($categories)) {
            $defaultCategories = [
                ['name' => 'Cosmetics', 'slug' => 'cosmetics', 'description' => 'Beauty and cosmetic products'],
                ['name' => 'Medicine', 'slug' => 'medicine', 'description' => 'Health and medical products'],
                ['name' => 'Milk Products', 'slug' => 'milk-products', 'description' => 'Dairy and milk products'],
                ['name' => 'Sports', 'slug' => 'sports', 'description' => 'Sports and fitness equipment'],
                ['name' => 'Vegetables', 'slug' => 'vegetables', 'description' => 'Fresh vegetables and produce']
            ];

            $insertStmt = $conn->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
            foreach ($defaultCategories as $cat) {
                try {
                    $insertStmt->execute([$cat['name'], $cat['slug'], $cat['description']]);
                } catch (PDOException $insertError) {
                    // Category might already exist, continue
                }
            }

            // Try to fetch categories again
            $categoryStmt->execute();
            $categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// Fallback categories if database still fails
if (empty($categories)) {
    $categories = [
        ['category_id' => 1, 'name' => 'Cosmetics', 'slug' => 'cosmetics'],
        ['category_id' => 2, 'name' => 'Medicine', 'slug' => 'medicine'],
        ['category_id' => 3, 'name' => 'Milk Products', 'slug' => 'milk-products'],
        ['category_id' => 4, 'name' => 'Sports', 'slug' => 'sports'],
        ['category_id' => 5, 'name' => 'Vegetables', 'slug' => 'vegetables']
    ];
}

// Get products with filters
$whereClause = "WHERE p.is_active = 1";
$params = [];

// Category filter
$selectedCategory = null;
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $whereClause .= " AND p.category_id = ?";
    $params[] = $_GET['category'];
    
    // Get selected category name
    try {
        $catStmt = $conn->prepare("SELECT name FROM categories WHERE category_id = ?");
        $catStmt->execute([$_GET['category']]);
        $result = $catStmt->fetch(PDO::FETCH_ASSOC);
        $selectedCategory = $result ? $result['name'] : null;
    } catch (PDOException $e) {
        error_log("Error fetching selected category: " . $e->getMessage());
        $selectedCategory = null;
    }
}

// Search filter
$searchTerm = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = $_GET['search'];
    $whereClause .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $searchParam = '%' . $searchTerm . '%';
    $params[] = $searchParam;
    $params[] = $searchParam;
}

// Sorting options
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'name_asc';
$order_clause = "";
switch ($sort_by) {
    case 'name_asc':
        $order_clause = "ORDER BY p.name ASC";
        break;
    case 'name_desc':
        $order_clause = "ORDER BY p.name DESC";
        break;
    case 'price_asc':
        $order_clause = "ORDER BY p.price ASC";
        break;
    case 'price_desc':
        $order_clause = "ORDER BY p.price DESC";
        break;
    case 'newest':
        $order_clause = "ORDER BY p.created_at DESC";
        break;
    default:
        $order_clause = "ORDER BY p.name ASC";
}

// Get products
try {
    $productQuery = "
        SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image, p.created_at,
               c.name as category_name,
               COALESCE(AVG(r.rating), 0) as rating,
               COUNT(DISTINCT r.review_id) as review_count
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_reviews r ON p.product_id = r.product_id
        $whereClause
        GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.created_at, c.name
        $order_clause
    ";

    $productStmt = $conn->prepare($productQuery);
    $productStmt->execute($params);
    $products = $productStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching products: " . $e->getMessage());
    $products = [];
}

// Get cart count if user is logged in
$cartCount = 0;
if (isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->prepare("
            SELECT SUM(quantity)
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $cartCount = $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        // Ignore error
    }
}

include 'includes/header.php';
?>

<style>
/* Products Page Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --white: #ffffff;
    
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
    --radius-lg: 20px;
    --radius-md: 12px;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--light-color);
}

.products-header {
    background: var(--primary-gradient);
    color: white;
    padding: 60px 0 40px;
    margin-bottom: 40px;
}

.products-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.products-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.filters-section {
    background: white;
    border-radius: var(--radius-lg);
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
}

.filter-group {
    margin-bottom: 20px;
}

.filter-label {
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.category-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.category-btn {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    color: var(--dark-color);
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.category-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
}

.category-btn.active {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: white;
}

.search-sort-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 250px;
}

.search-input {
    border: 2px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.sort-select {
    border: 2px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-size: 1rem;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: #6b7280;
    font-size: 0.85rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
    line-height: 1.4;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.rating-stars {
    color: var(--warning-color);
    font-size: 0.9rem;
}

.rating-text {
    color: #6b7280;
    font-size: 0.85rem;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    margin-top: auto;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    color: white;
}

.btn-view-detail {
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    padding: 12px 16px;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-view-detail:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.no-products {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-products i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #d1d5db;
}

/* Responsive */
@media (max-width: 768px) {
    .products-title {
        font-size: 2rem;
    }
    
    .search-sort-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .category-filter {
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}
</style>

<!-- Products Header -->
<section class="products-header">
    <div class="container">
        <div class="text-center">
            <h1 class="products-title">
                <?php if ($selectedCategory): ?>
                    <?php echo htmlspecialchars($selectedCategory); ?>
                <?php elseif ($searchTerm): ?>
                    Search Results for "<?php echo htmlspecialchars($searchTerm); ?>"
                <?php else: ?>
                    All Products
                <?php endif; ?>
            </h1>
            <p class="products-subtitle">
                <?php if ($selectedCategory): ?>
                    Explore our <?php echo htmlspecialchars($selectedCategory); ?> collection
                <?php elseif ($searchTerm): ?>
                    Found <?php echo count($products); ?> products matching your search
                <?php else: ?>
                    Discover amazing products at unbeatable prices
                <?php endif; ?>
            </p>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<div class="container">
    <div class="filters-section">
        <!-- Category Filter -->
        <div class="filter-group">
            <div class="filter-label">Categories</div>
            <div class="category-filter">
                <a href="products_public.php" class="category-btn <?php echo !isset($_GET['category']) ? 'active' : ''; ?>">
                    All Products
                </a>
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <?php if (isset($category['category_id']) && isset($category['name'])): ?>
                            <a href="products_public.php?category=<?php echo htmlspecialchars($category['category_id']); ?>"
                               class="category-btn <?php echo (isset($_GET['category']) && $_GET['category'] == $category['category_id']) ? 'active' : ''; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">No categories available</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Search and Sort -->
        <div class="search-sort-row">
            <div class="search-box">
                <form method="GET" action="products_public.php">
                    <?php if (isset($_GET['category'])): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($_GET['category']); ?>">
                    <?php endif; ?>
                    <?php if (isset($_GET['sort'])): ?>
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($_GET['sort']); ?>">
                    <?php endif; ?>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control search-input"
                               placeholder="Search products..."
                               value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <div class="sort-box">
                <form method="GET" action="products_public.php" id="sortForm">
                    <?php if (isset($_GET['category'])): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($_GET['category']); ?>">
                    <?php endif; ?>
                    <?php if (isset($_GET['search'])): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search']); ?>">
                    <?php endif; ?>
                    <select name="sort" class="form-select sort-select" onchange="document.getElementById('sortForm').submit();">
                        <option value="name_asc" <?php echo $sort_by == 'name_asc' ? 'selected' : ''; ?>>Name A-Z</option>
                        <option value="name_desc" <?php echo $sort_by == 'name_desc' ? 'selected' : ''; ?>>Name Z-A</option>
                        <option value="price_asc" <?php echo $sort_by == 'price_asc' ? 'selected' : ''; ?>>Price Low to High</option>
                        <option value="price_desc" <?php echo $sort_by == 'price_desc' ? 'selected' : ''; ?>>Price High to Low</option>
                        <option value="newest" <?php echo $sort_by == 'newest' ? 'selected' : ''; ?>>Newest First</option>
                    </select>
                </form>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <?php if (!empty($products)): ?>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <div class="product-image">
                        <?php if (($product['stock'] ?? 0) <= 0): ?>
                            <div class="product-badge bg-secondary">Out of Stock</div>
                        <?php elseif (isset($product['created_at']) && strtotime($product['created_at']) > strtotime('-7 days')): ?>
                            <div class="product-badge bg-success">New</div>
                        <?php endif; ?>

                        <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                             alt="<?php echo htmlspecialchars($product['name'] ?? 'Product'); ?>"
                             class="product-img">
                    </div>

                    <div class="product-info">
                        <div class="product-category"><?php echo htmlspecialchars($product['category_name'] ?? 'General'); ?></div>
                        <h6 class="product-title"><?php echo htmlspecialchars($product['name'] ?? 'Product Name'); ?></h6>

                        <div class="product-rating">
                            <div class="rating-stars">
                                <?php
                                $rating = round($product['rating'] ?? 0);
                                for ($i = 1; $i <= 5; $i++) {
                                    echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                }
                                ?>
                            </div>
                            <span class="rating-text">(<?php echo $product['review_count'] ?? 0; ?>)</span>
                        </div>

                        <div class="product-price">
                            Rp <?php echo number_format($product['price'] ?? 0, 0, ',', '.'); ?>
                        </div>

                        <div class="product-actions">
                            <?php if (($product['stock'] ?? 0) > 0): ?>
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <button class="btn btn-add-to-cart"
                                            data-product-id="<?php echo $product['product_id'] ?? 0; ?>"
                                            onclick="alert('Function about to be called...'); console.log('About to call addToCartNow with:', <?php echo $product['product_id'] ?? 0; ?>); addToCartNow(<?php echo $product['product_id'] ?? 0; ?>); alert('Function call completed');">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                <?php else: ?>
                                    <a href="login.php" class="btn btn-add-to-cart">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login to Buy
                                    </a>
                                <?php endif; ?>
                                <a href="product-detail.php?id=<?php echo $product['product_id'] ?? 0; ?>" class="btn-view-detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                            <?php else: ?>
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-times me-2"></i>Out of Stock
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="no-products">
            <i class="fas fa-box-open"></i>
            <h4>No Products Found</h4>
            <p>
                <?php if ($selectedCategory): ?>
                    No products available in this category at the moment.
                <?php elseif ($searchTerm): ?>
                    No products match your search criteria. Try different keywords.
                <?php else: ?>
                    No products available at the moment. Please check back later.
                <?php endif; ?>
            </p>
            <a href="products_public.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>View All Products
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Load jQuery FIRST -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- ULTRA SIMPLE TEST SCRIPT -->
<script>
console.log('🚀 Starting add to cart script...');

// WORKING ADD TO CART FUNCTION
function addToCartNow(productId) {
    console.log('🛒 addToCartNow called with productId:', productId);

    if (!productId || productId <= 0) {
        alert('❌ Invalid product ID: ' + productId);
        return;
    }

    // Show loading message
    alert('🔄 Adding product ' + productId + ' to cart...');

    // Use fetch to send AJAX request
    fetch('ajax/test_add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'product_id=' + encodeURIComponent(productId) + '&quantity=1'
    })
    .then(response => {
        console.log('📡 Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('📄 Response text:', text);
        try {
            const data = JSON.parse(text);
            console.log('📊 Parsed data:', data);

            if (data.success) {
                alert('✅ SUCCESS!\n' + data.message + '\nTotal items in cart: ' + (data.data.cart_count || 0));
            } else {
                alert('❌ FAILED!\n' + data.message);
                console.log('Debug info:', data.debug);
            }
        } catch (e) {
            console.error('❌ JSON Parse Error:', e);
            alert('❌ JSON Parse Error!\nResponse: ' + text.substring(0, 200));
        }
    })
    .catch(error => {
        console.error('❌ Fetch Error:', error);
        alert('❌ Network Error!\n' + error.message);
    });
}

// Make function globally accessible
window.addToCartNow = addToCartNow;

// Test if function is accessible
console.log('✅ addToCartNow function defined:', typeof addToCartNow);
console.log('✅ window.addToCartNow defined:', typeof window.addToCartNow);

// Test function with a simple call
try {
    console.log('🧪 Testing function definition...');
    if (typeof addToCartNow === 'function') {
        console.log('✅ Function is callable');
    } else {
        console.error('❌ Function is not callable');
    }
} catch (e) {
    console.error('❌ Error testing function:', e);
}

// Define function immediately (not inside document ready)
function testAddToCart(productId) {
    console.log('🛒 testAddToCart called with productId:', productId);

    if (!productId || productId <= 0) {
        alert('❌ Invalid product ID: ' + productId);
        return;
    }

    // Show loading message
    alert('� Testing add to cart for product ' + productId + '...');

    // Prepare the data to send
    const postData = 'product_id=' + encodeURIComponent(productId) + '&quantity=1';
    console.log('📤 POST data being sent:', postData);

    // Use vanilla JavaScript fetch (no jQuery dependency)
    fetch('ajax/test_add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: postData
    })
    .then(response => {
        console.log('📡 Response received:', response);
        return response.text();
    })
    .then(text => {
        console.log('📄 Response text:', text);
        try {
            const data = JSON.parse(text);
            console.log('📊 Parsed data:', data);

            if (data.success) {
                alert('✅ SUCCESS!\n' + data.message + '\nCart items: ' + data.data.cart_count);
            } else {
                alert('❌ FAILED!\n' + data.message + '\n\nDebug info:\n' + JSON.stringify(data.debug, null, 2));
            }
        } catch (e) {
            console.error('❌ JSON Parse Error:', e);
            alert('❌ JSON Parse Error!\nResponse: ' + text);
        }
    })
    .catch(error => {
        console.error('❌ Fetch Error:', error);
        alert('❌ Network Error!\n' + error.message);
    });
}

// Make function globally accessible immediately
window.testAddToCart = testAddToCart;
window.addToCart = testAddToCart;
window.handleAddToCart = testAddToCart;
window.addProductToCart = testAddToCart;

console.log('✅ Functions defined globally:', {
    testAddToCart: typeof testAddToCart,
    addToCart: typeof addToCart,
    handleAddToCart: typeof handleAddToCart
});

// Set up event delegation when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 DOM loaded, setting up event delegation...');


    // Set up event delegation for add to cart buttons (using vanilla JS)
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-add-to-cart') || e.target.closest('.btn-add-to-cart')) {
            e.preventDefault();
            console.log('🎯 Add to cart button clicked!');

            const button = e.target.classList.contains('btn-add-to-cart') ? e.target : e.target.closest('.btn-add-to-cart');
            const productId = button.getAttribute('data-product-id');

            console.log('🔍 Button element:', button);
            console.log('🔍 All button attributes:', button.attributes);
            console.log('� data-product-id attribute:', button.getAttribute('data-product-id'));
            console.log('🔍 dataset.productId:', button.dataset.productId);
            console.log('�📦 Product ID from button:', productId);

            if (productId && productId > 0) {
                console.log('🚀 Calling testAddToCart with ID:', productId);
                testAddToCart(parseInt(productId));
            } else {
                console.error('❌ Invalid product ID:', productId);
                console.error('❌ Button HTML:', button.outerHTML);
                alert('❌ Error: Invalid product ID: ' + productId + '\nCheck console for button details');
            }
        }
    });

    console.log('✅ Event delegation set up successfully!');

    // Debug: Check if user is logged in
    <?php if (isset($_SESSION['user_id'])): ?>
        console.log('✅ User is logged in with ID: <?php echo $_SESSION['user_id']; ?>');
    <?php else: ?>
        console.log('❌ User is NOT logged in');
    <?php endif; ?>
});



// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease;
    `;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    .notification-toast {
        animation: slideInRight 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
