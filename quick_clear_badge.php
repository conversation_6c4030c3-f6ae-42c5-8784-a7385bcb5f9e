<?php
/**
 * Simple script to clear notification badge
 * Direct database connection to avoid any include issues
 */

session_start();

// Direct database connection
$host = 'localhost';
$dbname = 'db_tewuneed';
$username = 'root';
$password = '';

try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Please log in first. <a href='login.php'>Login here</a>");
}

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? 'show';

echo "<h2>🔔 Clear Notification Badge</h2>";
echo "<p>User ID: {$user_id}</p>";

try {
    // First, let's see what notifications exist
    $stmt = $conn->prepare("
        SELECT notification_id, title, message, is_read, created_at 
        FROM order_notifications 
        WHERE user_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    $notifications = $stmt->fetchAll();
    
    echo "<h3>📋 Current Notifications (" . count($notifications) . " total)</h3>";
    
    if (count($notifications) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Read</th><th>Created</th></tr>";
        
        $unread_count = 0;
        foreach ($notifications as $notif) {
            $read_status = $notif['is_read'] ? '✅ Read' : '❌ Unread';
            if (!$notif['is_read']) $unread_count++;
            
            echo "<tr>";
            echo "<td>{$notif['notification_id']}</td>";
            echo "<td>" . htmlspecialchars($notif['title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($notif['message'], 0, 50)) . "...</td>";
            echo "<td>{$read_status}</td>";
            echo "<td>{$notif['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>🔴 Unread notifications causing badge: {$unread_count}</strong></p>";
        
        // Action buttons
        if ($action === 'show') {
            echo "<h3>🛠️ Actions</h3>";
            echo "<a href='?action=mark_read' style='background: green; color: white; padding: 10px; text-decoration: none; margin: 5px;'>✅ Mark All as Read</a>";
            echo "<a href='?action=delete' style='background: red; color: white; padding: 10px; text-decoration: none; margin: 5px;' onclick='return confirm(\"Delete all notifications?\")'>🗑️ Delete All</a>";
        }
        
        // Perform actions
        if ($action === 'mark_read') {
            $stmt = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $affected = $stmt->rowCount();
            
            echo "<div style='background: lightgreen; padding: 10px; margin: 10px 0;'>";
            echo "✅ <strong>Success!</strong> Marked {$affected} notifications as read.";
            echo "</div>";
            
            echo "<script>";
            echo "setTimeout(function() { window.location.href = 'quick_clear_badge.php'; }, 2000);";
            echo "</script>";
        }
        
        if ($action === 'delete') {
            $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $affected = $stmt->rowCount();
            
            echo "<div style='background: lightcoral; padding: 10px; margin: 10px 0;'>";
            echo "🗑️ <strong>Deleted!</strong> Removed {$affected} notifications.";
            echo "</div>";
            
            echo "<script>";
            echo "setTimeout(function() { window.location.href = 'quick_clear_badge.php'; }, 2000);";
            echo "</script>";
        }
        
    } else {
        echo "<p>✅ No notifications found. The badge should not be showing.</p>";
    }
    
    // Check current unread count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM order_notifications WHERE user_id = ? AND is_read = FALSE");
    $stmt->execute([$user_id]);
    $current_unread = $stmt->fetchColumn();
    
    echo "<h3>📊 Current Status</h3>";
    echo "<p><strong>Unread notifications: {$current_unread}</strong></p>";
    
    if ($current_unread == 0) {
        echo "<div style='background: lightgreen; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "🎉 <strong>Perfect!</strong> No unread notifications. The badge should disappear now.";
        echo "<br><small>If the badge still shows, try refreshing the page or wait 30 seconds for auto-update.</small>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: lightcoral; padding: 10px;'>";
    echo "❌ <strong>Error:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Navigation</h3>";
echo "<a href='index.php'>🏠 Home</a> | ";
echo "<a href='user_notifications.php'>🔔 Notifications Page</a> | ";
echo "<a href='quick_clear_badge.php'>🔄 Refresh This Page</a>";

echo "<hr>";
echo "<h3>🧪 Test Badge Update</h3>";
echo "<button onclick='testBadgeUpdate()' style='background: blue; color: white; padding: 10px;'>🔄 Test Header Update</button>";

echo "<script>";
echo "function testBadgeUpdate() {";
echo "  const badge = document.getElementById('headerNotificationBadge');";
echo "  if (badge) {";
echo "    badge.style.display = 'none';";
echo "    alert('Badge hidden! If it reappears, there are still unread notifications in the database.');";
echo "  } else {";
echo "    alert('No badge found in header. This might mean the badge is already hidden or you are not on a page with the header.');";
echo "  }";
echo "}";
echo "</script>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
a { display: inline-block; margin: 5px; }
</style>
