<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/../config/database.php';

/**
 * Fungsi untuk mendapatkan kelas badge status pesanan
 *
 * @param string $status Status pesanan (dibuat, diproses, dikirim, terkirim, dibatalkan)
 * @return string Kelas CSS untuk badge status
 */
function getOrderStatusBadgeClass($status) {
    // Normalize status to lowercase
    $status = strtolower(trim($status));

    // Pemetaan status ke kelas badge - konsisten untuk semua variasi
    $status_map = [
        'dibuat' => 'warning',
        'pending' => 'warning',
        'diproses' => 'info',
        'processing' => 'info',
        'dikirim' => 'primary',
        'shipped' => 'primary',
        'terkirim' => 'success',
        'delivered' => 'success',
        'completed' => 'success',
        'dibatalkan' => 'danger',
        'cancelled' => 'danger',
        'canceled' => 'danger'
    ];
    // Kembalikan kelas yang sesuai atau default
    return $status_map[$status] ?? 'warning'; // Default ke warning instead of secondary
}




/**
 * Fungsi untuk mendapatkan ikon status pesanan
 *
 * @param string $status Status pesanan
 * @return string Kelas CSS untuk ikon
 */
function getOrderStatusIcon($status) {
    // Normalize status to lowercase
    $status = strtolower(trim($status));

    // Pemetaan status ke ikon - konsisten untuk semua variasi
    $icon_map = [
        'dibuat' => 'fa-clock',
        'pending' => 'fa-clock',
        'diproses' => 'fa-spinner fa-spin',
        'processing' => 'fa-spinner fa-spin',
        'dikirim' => 'fa-truck',
        'shipped' => 'fa-truck',
        'terkirim' => 'fa-check-circle',
        'delivered' => 'fa-check-circle',
        'completed' => 'fa-check-circle',
        'dibatalkan' => 'fa-times-circle',
        'cancelled' => 'fa-times-circle',
        'canceled' => 'fa-times-circle'
    ];

    // Kembalikan ikon yang sesuai atau default ke clock
    return $icon_map[$status] ?? 'fa-clock';
}

/**
 * Mendapatkan daftar pesanan terbaru untuk dashboard
 *
 * @param int $limit Jumlah maksimal pesanan yang dikembalikan
 * @return array Daftar pesanan terbaru
 */
function getRecentOrders($limit = 5) {
    global $conn;
    $orders = [];

    try {
        // Periksa struktur tabel orders untuk konsistensi
        $orderDateColumn = "created_at";  // Default
        $statusColumn = "order_status";    // Default

        // Periksa apakah kolom order_date ada
        $dateColCheck = $conn->query("SHOW COLUMNS FROM orders LIKE 'order_date'");
        if ($dateColCheck->rowCount() > 0) {
            $orderDateColumn = "order_date";
        }

        // Query untuk mendapatkan pesanan terbaru dengan informasi pelanggan
        $query = "
            SELECT o.*, u.full_name, u.username, u.email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.user_id
            ORDER BY o.{$orderDateColumn} DESC
            LIMIT ?
        ";

        $stmt = $conn->prepare($query);
        $stmt->execute([$limit]);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Jika tidak ada total_amount, coba hitung dari order_items
        foreach ($orders as &$order) {
            if (!isset($order['total_amount']) || empty($order['total_amount'])) {
                try {
                    $stmtTotal = $conn->prepare("
                        SELECT SUM(price * quantity) as total
                        FROM order_items
                        WHERE order_id = ?
                    ");
                    $stmtTotal->execute([$order['order_id']]);
                    $total = $stmtTotal->fetchColumn();
                    $order['total_amount'] = $total ?: 0;
                } catch (Exception $e) {
                    $order['total_amount'] = 0;
                }
            }

            // Tambahkan informasi customer yang konsisten
            if (!isset($order['customer_name']) || empty($order['customer_name'])) {
                if (isset($order['full_name']) && !empty($order['full_name'])) {
                    $order['customer_name'] = $order['full_name'];
                } elseif (isset($order['username']) && !empty($order['username'])) {
                    $order['customer_name'] = $order['username'];
                } else {
                    $order['customer_name'] = "Guest";
                }
            }
        }
    } catch (Exception $e) {
        error_log("Error mendapatkan pesanan terbaru: " . $e->getMessage());
    }

    return $orders;
}

/**
 * Fungsi untuk mendapatkan label tampilan untuk status pesanan
 *
 * @param string $status Status pesanan internal
 * @return string Label tampilan yang user-friendly
 */
function getOrderStatusLabel($status) {
    // Normalize status to lowercase
    $status = strtolower(trim($status));

    // Pemetaan status ke label tampilan - konsisten untuk semua variasi
    $label_map = [
        'dibuat' => 'Pending',
        'pending' => 'Pending',
        'diproses' => 'Processing',
        'processing' => 'Processing',
        'dikirim' => 'Shipped',
        'shipped' => 'Shipped',
        'terkirim' => 'Delivered',
        'delivered' => 'Delivered',
        'completed' => 'Completed',
        'dibatalkan' => 'Cancelled',
        'cancelled' => 'Cancelled',
        'canceled' => 'Cancelled'
    ];

    // Kembalikan label yang sesuai atau default ke Pending
    return $label_map[$status] ?? 'Pending';
}

/**
 * Generate HTML for status badge with icon
 *
 * @param string $status Status pesanan
 * @param bool $with_icon Whether to include icon
 * @return string HTML for status badge
 */
function getStatusBadgeHtml($status, $with_icon = true) {
    // Normalize status to lowercase
    $status = strtolower(trim($status));

    $badge_class = getOrderStatusBadgeClass($status);
    $icon_class = getOrderStatusIcon($status);
    $status_label = getOrderStatusLabel($status);

    $icon_html = $with_icon ? "<i class='fas {$icon_class} me-1'></i> " : '';
    return "<span class='badge bg-{$badge_class}'>{$icon_html}{$status_label}</span>";
}

/**
 * Update order status with proper validation and logging
 * NOTE: This function has been moved to includes/order_status_functions.php
 * This is kept for backward compatibility but will be deprecated
 *
 * @deprecated Use updateOrderStatus from order_status_functions.php instead
 */
function updateOrderStatusOld($order_id, $new_status, $note = '', $admin_id = null, $notify_customer = true, $delete_if_cancelled = false) {
    $conn = getConnection();
    $result = [
        'success' => false,
        'message' => '',
        'affected_rows' => 0
    ];

    try {
        // Validate input
        $order_id = (int)$order_id;
        if ($order_id <= 0) {
            throw new Exception("Invalid order ID: {$order_id}");
        }

        // Validate order exists first
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            throw new Exception("Order not found with ID: {$order_id}");
        }

        // Get current status from any available column
        $old_status = $order['order_status'] ?? $order['status'] ?? 'pending';

        // Begin transaction
        $conn->beginTransaction();

        // Get table structure
        $stmt = $conn->prepare("DESCRIBE orders");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $column_names = array_column($columns, 'Field');

        $has_order_status = in_array('order_status', $column_names);
        $has_status = in_array('status', $column_names);
        $has_updated_at = in_array('updated_at', $column_names);

        // Build update query
        $update_parts = [];
        $params = [];

        if ($has_order_status) {
            $update_parts[] = 'order_status = ?';
            $params[] = $new_status;
        }

        if ($has_status) {
            $update_parts[] = 'status = ?';
            $params[] = $new_status;
        }

        if ($has_updated_at) {
            $update_parts[] = 'updated_at = NOW()';
        }

        if (empty($update_parts)) {
            throw new Exception("No status columns found in orders table");
        }

        $params[] = $order_id;

        $sql = "UPDATE orders SET " . implode(', ', $update_parts) . " WHERE order_id = ?";

        // Execute update
        $stmt = $conn->prepare($sql);
        $update_success = $stmt->execute($params);
        $affected_rows = $stmt->rowCount();

        if (!$update_success) {
            throw new Exception("Failed to execute update query");
        }

        if ($affected_rows === 0) {
            // Double check if order still exists
            $stmt = $conn->prepare("SELECT COUNT(*) FROM orders WHERE order_id = ?");
            $stmt->execute([$order_id]);
            $exists = $stmt->fetchColumn();

            if ($exists == 0) {
                throw new Exception("Order was deleted during update process");
            } else {
                throw new Exception("Update query executed but no rows were affected. Current status may already be '{$new_status}'");
            }
        }

        // Add to status history
        try {
            $stmt = $conn->prepare("
                INSERT INTO order_status_history (
                    order_id, admin_id, status, old_status, new_status, note, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$order_id, $admin_id, $new_status, $old_status, $new_status, $note]);
        } catch (PDOException $e) {
            // History table might not exist, continue anyway
            error_log("Could not insert into order_status_history: " . $e->getMessage());
        }

        // Handle cancellation deletion
        if ($delete_if_cancelled && ($new_status === 'dibatalkan' || $new_status === 'cancelled')) {
            // Delete order items first
            $stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
            $stmt->execute([$order_id]);

            // Delete order
            $stmt = $conn->prepare("DELETE FROM orders WHERE order_id = ?");
            $stmt->execute([$order_id]);

            $result['message'] = "Order cancelled and deleted successfully";
        } else {
            $result['message'] = "Order status updated successfully from '{$old_status}' to '{$new_status}'";
        }

        // Sync status to customer view
        syncOrderStatusToCustomer($order_id, $new_status, $note);

        $conn->commit();
        $result['success'] = true;
        $result['affected_rows'] = $stmt->rowCount();

    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $result['success'] = false;
        $result['message'] = $e->getMessage();
        error_log("Error updating order status: " . $e->getMessage());
    }

    return $result;
}

/**
 * Sync order status to customer view
 *
 * @param int $order_id Order ID
 * @param string $status New status
 * @param string $note Optional note
 * @return bool Success status
 */
function syncOrderStatusToCustomer($order_id, $status, $note = '') {
    try {
        $conn = getConnection();

        // Update customer order status table if exists
        try {
            $stmt = $conn->prepare("
                UPDATE customer_orders
                SET status = ?, updated_at = NOW()
                WHERE order_id = ?
            ");
            $stmt->execute([$status, $order_id]);
        } catch (PDOException $e) {
            // Table might not exist, continue
        }

        // Store status update in session for real-time sync
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['order_status_updates'])) {
            $_SESSION['order_status_updates'] = [];
        }

        $_SESSION['order_status_updates'][$order_id] = [
            'status' => $status,
            'note' => $note,
            'timestamp' => time()
        ];

        return true;
    } catch (Exception $e) {
        error_log("Error syncing order status to customer: " . $e->getMessage());
        return false;
    }
}

/**
 * Get order status updates for customer
 *
 * @param int $customer_id Customer ID
 * @return array Order status updates
 */
function getOrderStatusUpdatesForCustomer($customer_id) {
    try {
        $conn = getConnection();

        // Get recent status updates for customer's orders
        $stmt = $conn->prepare("
            SELECT o.order_id, o.order_status, o.updated_at,
                   osh.note, osh.created_at as status_changed_at
            FROM orders o
            LEFT JOIN order_status_history osh ON o.order_id = osh.order_id
            WHERE o.user_id = ?
            AND o.updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY o.updated_at DESC
        ");
        $stmt->execute([$customer_id]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting order status updates for customer: " . $e->getMessage());
        return [];
    }
}



/**
 * Common utility functions for the application
 */

/**
 * Get database connection
 */
function getDatabaseConnection() {
    global $conn;
    return $conn;
}



/**
 * Get user by ID
 */
function getUserById($id) {
    try {
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting user: " . $e->getMessage());
        return false;
    }
}

/**
 * Get product by ID
 */
function getProductById($id) {
    try {
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("
            SELECT p.*, c.NAME as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.product_id = ?
        ");
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($product) {
            // Format price as float
            $product['price'] = (float)$product['price'];
            // Ensure stock is integer
            $product['stock'] = (int)$product['stock'];
            // Set default image if none
            if (empty($product['image_url'])) {
                $product['image_url'] = 'assets/images/default-product.png';
            }
        }

        return $product;
    } catch (PDOException $e) {
        error_log("Error getting product: " . $e->getMessage());
        return false;
    }
}

/**
 * Get category by ID
 */
function getCategoryById($id) {
    try {
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting category: " . $e->getMessage());
        return false;
    }
}

/**
 * Format price
 */
function formatPrice($price) {
    return CURRENCY_SYMBOL . ' ' . number_format($price, 0, ',', '.');
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $randomString;
}

/**
 * Upload file
 */
function uploadFile($file, $destination) {
    try {
        if (!isset($file['error']) || is_array($file['error'])) {
            throw new RuntimeException('Invalid parameters.');
        }

        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                throw new RuntimeException('Exceeded filesize limit.');
            default:
                throw new RuntimeException('Unknown errors.');
        }

        if ($file['size'] > MAX_FILE_SIZE) {
            throw new RuntimeException('Exceeded filesize limit.');
        }

        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mime_type = $finfo->file($file['tmp_name']);

        if (!in_array($mime_type, ALLOWED_IMAGE_TYPES)) {
            throw new RuntimeException('Invalid file format.');
        }

        $filename = generateRandomString() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
        $filepath = UPLOAD_DIR . $filename;

        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new RuntimeException('Failed to move uploaded file.');
        }

        return $filename;
    } catch (RuntimeException $e) {
        error_log("File Upload Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Validate password strength
 */
function validatePassword($password) {
    return strlen($password) >= 8 &&
           preg_match('/[A-Z]/', $password) &&
           preg_match('/[a-z]/', $password) &&
           preg_match('/[0-9]/', $password);
}

/**
 * Check if user is logged in
 * Note: Function moved to firebase_auth.php to avoid conflicts
 */

/**
 * Check if user is admin
 */
function isAdmin($userId) {
    try {
        global $conn; // Use global connection if available

        // Try to get connection from function or use global connection
        $connection = getDatabaseConnection();

        // If both options fail, try to establish a new connection
        if (!$connection && !$conn) {
            // Define database constants if not already defined
            if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
            if (!defined('DB_NAME')) define('DB_NAME', 'db_tewuneed');
            if (!defined('DB_USER')) define('DB_USER', 'root');
            if (!defined('DB_PASS')) define('DB_PASS', '');

            try {
                $connection = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
                $connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            } catch (PDOException $e) {
                error_log("Failed to establish database connection in isAdmin(): " . $e->getMessage());
                return false;
            }
        }

        // Use whichever connection is available
        $dbConn = $connection ?: $conn;

        if (!$dbConn) {
            error_log("No database connection available in isAdmin()");
            return false;
        }

        $stmt = $dbConn->prepare("SELECT role FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        return $user && $user['role'] === 'admin';
    } catch (PDOException $e) {
        error_log("Error checking admin status: " . $e->getMessage());
        return false;
    }
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION[$type] = $message;

    // Clear any output buffer to prevent "headers already sent" errors
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Only send header if headers haven't been sent yet
    if (!headers_sent()) {
        header("Location: $url");
        exit();
    } else {
        // Fallback: use JavaScript redirect if headers already sent
        echo '<script type="text/javascript">window.location.href="' . htmlspecialchars($url) . '";</script>';
        echo '<noscript><meta http-equiv="refresh" content="0;url=' . htmlspecialchars($url) . '" /></noscript>';
        exit();
    }
}

/**
 * Set flash message
 */
function setFlashMessage($type, $message, $duration = 5) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message,
        'duration' => $duration
    ];
}

/**
 * Get flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

/**
 * Redirect to URL
 */
function redirect($url) {
    // Clear any output buffer to prevent "headers already sent" errors
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Only send header if headers haven't been sent yet
    if (!headers_sent()) {
        header("Location: $url");
        exit();
    } else {
        // Fallback: use JavaScript redirect if headers already sent
        echo '<script type="text/javascript">window.location.href="' . htmlspecialchars($url) . '";</script>';
        echo '<noscript><meta http-equiv="refresh" content="0;url=' . htmlspecialchars($url) . '" /></noscript>';
        exit();
    }
}

/**
 * Generate unique order number
 */
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

/**
 * Sync Firebase user with local database
 */
function syncFirebaseUserToDatabase($firebase_uid, $email, $display_name = null, $photo_url = null) {
    global $conn;

    try {
        // Check if firebase_user_id column exists
        $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'firebase_user_id'");
        $hasFirebaseColumn = $stmt->rowCount() > 0;

        if ($hasFirebaseColumn) {
            // Check if user exists by Firebase UID
            $stmt = $conn->prepare("SELECT user_id FROM users WHERE firebase_user_id = ?");
            $stmt->execute([$firebase_uid]);
            $user = $stmt->fetch();

            if ($user) {
                // User exists, update info if needed
                // Check if updated_at column exists
                $stmt_check = $conn->query("SHOW COLUMNS FROM users LIKE 'updated_at'");
                $has_updated_at = $stmt_check->rowCount() > 0;

                if ($has_updated_at) {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET email = ?, full_name = COALESCE(?, full_name), updated_at = NOW()
                        WHERE firebase_user_id = ?
                    ");
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET email = ?, full_name = COALESCE(?, full_name)
                        WHERE firebase_user_id = ?
                    ");
                }
                $stmt->execute([$email, $display_name, $firebase_uid]);
                return $user['user_id'];
            }
        }

        // Check if user exists by email (for migration or if no firebase column)
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user) {
            // Check if updated_at column exists
            $stmt_check = $conn->query("SHOW COLUMNS FROM users LIKE 'updated_at'");
            $has_updated_at = $stmt_check->rowCount() > 0;

            // Update existing user with Firebase UID (if column exists)
            if ($hasFirebaseColumn) {
                if ($has_updated_at) {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET firebase_user_id = ?, full_name = COALESCE(?, full_name), updated_at = NOW()
                        WHERE user_id = ?
                    ");
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET firebase_user_id = ?, full_name = COALESCE(?, full_name)
                        WHERE user_id = ?
                    ");
                }
                $stmt->execute([$firebase_uid, $display_name, $user['user_id']]);
            } else {
                // Just update the display name if no firebase column
                if ($has_updated_at) {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = COALESCE(?, full_name), updated_at = NOW()
                        WHERE user_id = ?
                    ");
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = COALESCE(?, full_name)
                        WHERE user_id = ?
                    ");
                }
                $stmt->execute([$display_name, $user['user_id']]);
            }
            return $user['user_id'];
        }

        // Create new user
        $username = $display_name ?: explode('@', $email)[0];

        // Check if created_at column exists
        $stmt_check = $conn->query("SHOW COLUMNS FROM users LIKE 'created_at'");
        $has_created_at = $stmt_check->rowCount() > 0;

        if ($hasFirebaseColumn) {
            if ($has_created_at) {
                $stmt = $conn->prepare("
                    INSERT INTO users (firebase_user_id, email, full_name, username, role, created_at)
                    VALUES (?, ?, ?, ?, 'customer', NOW())
                ");
            } else {
                $stmt = $conn->prepare("
                    INSERT INTO users (firebase_user_id, email, full_name, username, role)
                    VALUES (?, ?, ?, ?, 'customer')
                ");
            }
            $stmt->execute([$firebase_uid, $email, $display_name, $username]);
        } else {
            // Fallback for tables without firebase_user_id column
            if ($has_created_at) {
                $stmt = $conn->prepare("
                    INSERT INTO users (email, full_name, username, role, created_at)
                    VALUES (?, ?, ?, 'customer', NOW())
                ");
            } else {
                $stmt = $conn->prepare("
                    INSERT INTO users (email, full_name, username, role)
                    VALUES (?, ?, ?, 'customer')
                ");
            }
            $stmt->execute([$email, $display_name, $username]);
        }

        return $conn->lastInsertId();

    } catch (PDOException $e) {
        error_log("Error syncing Firebase user: " . $e->getMessage());
        error_log("Firebase UID: " . $firebase_uid . ", Email: " . $email);
        return false;
    }
}

/**
 * Get or create cart for user (Firebase compatible)
 */
function getOrCreateCart($user_id) {
    global $conn;

    try {
        // Handle Firebase users - sync first if needed
        if (isset($_SESSION['firebase_user_id']) && isset($_SESSION['user_email'])) {
            $local_user_id = syncFirebaseUserToDatabase(
                $_SESSION['firebase_user_id'],
                $_SESSION['user_email'],
                $_SESSION['user_name'] ?? null
            );

            if ($local_user_id) {
                $user_id = $local_user_id;
                // Update session with local user_id
                $_SESSION['local_user_id'] = $local_user_id;
            }
        }

        // Verify user exists
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        if (!$stmt->fetch()) {
            throw new Exception("User does not exist");
        }

        // Check if user has an existing cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart = $stmt->fetch();

        if ($cart) {
            return $cart['cart_id'];
        }

        // Create new cart
        $stmt = $conn->prepare("INSERT INTO carts (user_id, created_at, updated_at) VALUES (?, NOW(), NOW())");
        $stmt->execute([$user_id]);
        return $conn->lastInsertId();

    } catch (PDOException $e) {
        error_log("Error in getOrCreateCart: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        return false;
    } catch (Exception $e) {
        error_log("General error in getOrCreateCart: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        return false;
    }
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Get pagination
 */
function getPagination($total_items, $current_page = 1, $items_per_page = ITEMS_PER_PAGE) {
    $total_pages = ceil($total_items / $items_per_page);
    $current_page = max(1, min($current_page, $total_pages));

    $pagination = [
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'items_per_page' => $items_per_page,
        'total_items' => $total_items,
        'offset' => ($current_page - 1) * $items_per_page
    ];

    return $pagination;
}

/**
 * Generate pagination HTML
 */
function generatePaginationHtml($pagination, $url_pattern) {
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

    // Previous button
    $html .= '<li class="page-item ' . ($pagination['current_page'] <= 1 ? 'disabled' : '') . '">';
    $html .= '<a class="page-link" href="' . sprintf($url_pattern, $pagination['current_page'] - 1) . '" aria-label="Previous">';
    $html .= '<span aria-hidden="true">&laquo;</span></a></li>';

    // Page numbers
    for ($i = 1; $i <= $pagination['total_pages']; $i++) {
        $html .= '<li class="page-item ' . ($i == $pagination['current_page'] ? 'active' : '') . '">';
        $html .= '<a class="page-link" href="' . sprintf($url_pattern, $i) . '">' . $i . '</a></li>';
    }

    // Next button
    $html .= '<li class="page-item ' . ($pagination['current_page'] >= $pagination['total_pages'] ? 'disabled' : '') . '">';
    $html .= '<a class="page-link" href="' . sprintf($url_pattern, $pagination['current_page'] + 1) . '" aria-label="Next">';
    $html .= '<span aria-hidden="true">&raquo;</span></a></li>';

    $html .= '</ul></nav>';
    return $html;
}

/**
 * Log user activity
 * @param int $userId
 * @param string $action
 * @param string $details
 * @return bool
 */
function logActivity($userId, $action, $details = '') {
    try {
        global $conn;
        // Check if connection is valid
        if (!$conn) {
            error_log("Database connection failed in logActivity");
            return false;
        }
        // Check if activity_logs table exists
        try {
            $checkTable = $conn->query("SHOW TABLES LIKE 'activity_logs'");
            if ($checkTable->rowCount() == 0) {
                // Create activity_logs table if it doesn't exist
                $createTable = "CREATE TABLE activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(255) NOT NULL,
                    details TEXT,
                    ip_address VARCHAR(45) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($createTable);
                error_log("Created activity_logs table");
            }
        } catch (PDOException $e) {
            error_log("Error checking/creating activity_logs table: " . $e->getMessage());
            // Continue execution, we'll attempt the insert anyway
        }
        $stmt = $conn->prepare("
            INSERT INTO activity_logs (user_id, action, details, ip_address)
            VALUES (?, ?, ?, ?)
        ");
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
        return $stmt->execute([
            $userId,
            $action,
            $details,
            $ipAddress
        ]);
    } catch (PDOException $e) {
        error_log("Error logging activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Log user activity - wrapper for logActivity function
 *
 * @param int $userId User ID
 * @param string $action Description of the action
 * @param string $details Additional details (optional)
 * @return bool True on success, false on failure
 */
function logUserActivity($userId, $action, $details = '') {
    return logActivity($userId, $action, $details);
}

/**
 * Get user activity logs
 * @param int $user_id User ID
 * @param int $limit Number of logs to retrieve
 * @return array Array of activity logs
 */
function getUserActivityLogs($user_id, $limit = 10) {
    try {
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("
            SELECT * FROM activity_logs
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting user activity logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Send email
 */
function sendEmail($to, $subject, $message) {
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . SITE_NAME . ' <' . ADMIN_EMAIL . '>'
    ];

    return mail($to, $subject, $message, implode("\r\n", $headers));
}



/**
 * Create a new order
 * @param array $data Order data
 * @return array|false Order data with ID on success, false on failure
 */
function createOrder($data) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Generate order number
        $order_number = generateOrderNumber();

        // Insert order
        $stmt = $conn->prepare("
            INSERT INTO orders (
                order_number, user_id, name, email, phone, address,
                subtotal, shipping_cost, total_amount, payment_method
            ) VALUES (
                :order_number, :user_id, :name, :email, :phone, :address,
                :subtotal, :shipping_cost, :total_amount, :payment_method
            )
        ");

        $stmt->execute([
            'order_number' => $order_number,
            'user_id' => $data['user_id'],
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'address' => $data['address'],
            'subtotal' => $data['subtotal'],
            'shipping_cost' => $data['shipping_cost'],
            'total_amount' => $data['total_amount'],
            'payment_method' => $data['payment_method']
        ]);

        $order_id = $conn->lastInsertId();

        // Insert order items
        $stmt = $conn->prepare("
            INSERT INTO order_items (
                order_id, product_id, quantity, price, subtotal
            ) VALUES (
                :order_id, :product_id, :quantity, :price, :subtotal
            )
        ");

        foreach ($data['items'] as $item) {
            $stmt->execute([
                'order_id' => $order_id,
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'subtotal' => $item['price'] * $item['quantity']
            ]);

            // Update product stock
            $stock_stmt = $conn->prepare("
                UPDATE products
                SET stock = stock - :quantity
                WHERE product_id = :product_id AND stock >= :quantity
            ");

            $result = $stock_stmt->execute([
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity']
            ]);

            if ($stock_stmt->rowCount() === 0) {
                throw new Exception("Insufficient stock for product ID: " . $item['product_id']);
            }
        }

        // Create initial status history
        $stmt = $conn->prepare("
            INSERT INTO order_status_history (
                order_id, status, notes
            ) VALUES (
                :order_id, :status, :notes
            )
        ");

        $stmt->execute([
            'order_id' => $order_id,
            'status' => 'pending',
            'notes' => 'Order placed successfully'
        ]);

        $conn->commit();

        return [
            'order_id' => $order_id,
            'order_number' => $order_number
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Order Creation Error: " . $e->getMessage());
        return false;
    }
}



/**
 * Get order by order number
 * @param string $order_number Order number
 * @return array|false
 */
function getOrderByNumber($order_number) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT o.*,
                   COUNT(oi.order_item_id) as total_items,
                   osh.notes as status_notes,
                   osh.created_at as status_updated_at
            FROM orders o
            LEFT JOIN order_items oi ON o.order_id = oi.order_id
            LEFT JOIN order_status_history osh ON o.order_id = osh.order_id
                AND osh.created_at = (
                    SELECT MAX(created_at)
                    FROM order_status_history
                    WHERE order_id = o.order_id
                )
            WHERE o.order_number = ?
            GROUP BY o.order_id
        ");

        $stmt->execute([$order_number]);
        return $stmt->fetch();

    } catch (Exception $e) {
        error_log("Get Order Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get order items
 * @param int $order_id Order ID
 * @return array|false
 */
function getOrderItems($order_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT oi.*, p.name as product_name, p.image as product_image
            FROM order_items oi
            JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY oi.order_item_id ASC
        ");

        $stmt->execute([$order_id]);
        return $stmt->fetchAll();

    } catch (Exception $e) {
        error_log("Get Order Items Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get order status history (DEPRECATED)
 * NOTE: This function has been moved to includes/order_status_functions.php
 * @deprecated Use getOrderStatusHistory from order_status_functions.php instead
 * @param int $order_id Order ID
 * @return array|false
 */
function getOrderStatusHistoryOld($order_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT *
            FROM order_status_history
            WHERE order_id = ?
            ORDER BY created_at DESC
        ");

        $stmt->execute([$order_id]);
        return $stmt->fetchAll();

    } catch (Exception $e) {
        error_log("Get Order Status History Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user orders
 * @param int $user_id User ID
 * @return array|false
 */
function getUserOrders($user_id) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT o.*,
                   COUNT(oi.order_item_id) as total_items
            FROM orders o
            LEFT JOIN order_items oi ON o.order_id = oi.order_id
            WHERE o.user_id = ?
            GROUP BY o.order_id
            ORDER BY o.created_at DESC
        ");

        $stmt->execute([$user_id]);
        return $stmt->fetchAll();

    } catch (Exception $e) {
        error_log("Get User Orders Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Calculate shipping cost
 */
function calculateShippingCost($total) {
    return $total >= FREE_SHIPPING_THRESHOLD ? 0 : DEFAULT_SHIPPING_COST;
}

/**
 * Get order status badge
 */
function getOrderStatusBadge($status) {
    $badges = [
        'pending' => 'warning',
        'processing' => 'info',
        'shipped' => 'primary',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];

    $status_class = isset($badges[$status]) ? $badges[$status] : 'secondary';
    return '<span class="badge bg-' . $status_class . '">' . ucfirst($status) . '</span>';
}

/**
 * Get basic cart items from session
 * @return array Cart items array
 */
function getCartItems() {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    return $_SESSION['cart'];
}

/**
 * Get cart total items count
 * @return int Total items in cart
 */
function getCartItemsCount() {
    if (!isset($_SESSION['cart'])) {
        return 0;
    }

    $count = 0;
    foreach ($_SESSION['cart'] as $item) {
        $count += (int)$item['quantity'];
    }
    return $count;
}

/**
 * Add item to cart
 * @param int $product_id Product ID
 * @param int $quantity Quantity to add
 * @return array Response with status and message
 */
function addToCart($product_id, $quantity = 1) {
    global $conn;

    // Check if product exists and has stock
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        return [
            'success' => false,
            'message' => 'Product not found'
        ];
    }

    if ($product['stock'] < $quantity) {
        return [
            'success' => false,
            'message' => 'Not enough stock available'
        ];
    }

    // Initialize cart if not exists
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }

    // Add or update cart item
    if (isset($_SESSION['cart'][$product_id])) {
        $new_quantity = $_SESSION['cart'][$product_id]['quantity'] + $quantity;
        if ($new_quantity > $product['stock']) {
            return [
                'success' => false,
                'message' => 'Cannot add more of this item (stock limit reached)'
            ];
        }
        $_SESSION['cart'][$product_id]['quantity'] = $new_quantity;
    } else {
        $_SESSION['cart'][$product_id] = [
            'product_id' => $product_id,
            'name' => $product['NAME'],
            'price' => $product['price'],
            'quantity' => $quantity,
            'image' => $product['image']
        ];
    }

    return [
        'success' => true,
        'message' => 'Product added to cart successfully',
        'cart_count' => array_sum(array_column($_SESSION['cart'], 'quantity'))
    ];
}

/**
 * Update cart item quantity
 * @param int $product_id Product ID
 * @param int $quantity New quantity
 * @return array Response with status and message
 */
function updateCartItem($product_id, $quantity) {
    global $conn;

    if (!isset($_SESSION['cart'][$product_id])) {
        return [
            'success' => false,
            'message' => 'Product not in cart'
        ];
    }

    // Check stock
    $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $stock = $stmt->fetchColumn();

    if ($quantity > $stock) {
        return [
            'success' => false,
            'message' => 'Not enough stock available'
        ];
    }

    if ($quantity <= 0) {
        unset($_SESSION['cart'][$product_id]);
    } else {
        $_SESSION['cart'][$product_id]['quantity'] = $quantity;
    }

    return [
        'success' => true,
        'message' => 'Cart updated successfully',
        'cart_count' => array_sum(array_column($_SESSION['cart'], 'quantity'))
    ];
}

/**
 * Remove item from cart
 * @param int $product_id Product ID
 * @return array Response with status and message
 */
function removeCartItem($product_id) {
    if (isset($_SESSION['cart'][$product_id])) {
        unset($_SESSION['cart'][$product_id]);
    }

    return [
        'success' => true,
        'message' => 'Item removed from cart',
        'cart_count' => array_sum(array_column($_SESSION['cart'], 'quantity'))
    ];
}

/**
 * Get cart total
 * @return float Cart total amount
 */
function calculateCartTotal() {
    $total = 0;
    if (isset($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $item) {
            $total += $item['price'] * $item['quantity'];
        }
    }
    return $total;
}

/**
 * Get cart items with full product details
 * @return array Cart items with product details
 */
function getCartItemsWithDetails() {
    if (!isset($_SESSION['cart'])) {
        return [];
    }

    $items = [];
    foreach ($_SESSION['cart'] as $item) {
        $product = getProductById($item['product_id']);
        if ($product) {
            $items[] = [
                'product' => $product,
                'quantity' => (int)$item['quantity'],
                'subtotal' => $product['price'] * (int)$item['quantity']
            ];
        }
    }
    return $items;
}

/**
 * Generate CSRF token
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Create URL-friendly slug from string
 * @param string $string
 * @return string
 */
function createSlug($string) {
    $string = strtolower($string);
    $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
    $string = preg_replace('/[\s-]+/', ' ', $string);
    $string = preg_replace('/\s/', '-', $string);
    return $string;
}

