<?php
/**
 * Add to Cart - Database Version
 */

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');

// Fix the path - go up one level from ajax folder
require_once dirname(__DIR__) . '/includes/db_connect.php';
require_once dirname(__DIR__) . '/includes/functions.php';

// Log all received data for debugging
error_log("ADD TO CART - POST data: " . print_r($_POST, true));
error_log("ADD TO CART - SESSION data: " . print_r($_SESSION, true));

try {
    // Simple response function
    function respond($success, $message, $data = []) {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'debug' => [
                'post_data' => $_POST,
                'session_user_id' => $_SESSION['user_id'] ?? 'not set',
                'session_firebase_user_id' => $_SESSION['firebase_user_id'] ?? 'not set',
                'session_local_user_id' => $_SESSION['local_user_id'] ?? 'not set',
                'session_keys' => array_keys($_SESSION)
            ]
        ];
        echo json_encode($response);
        exit;
    }

    // Get product ID and quantity
    $product_id = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);

    error_log("Product ID: $product_id, Quantity: $quantity");

    if ($product_id <= 0) {
        respond(false, 'Invalid product ID: ' . $product_id);
    }

    if ($quantity <= 0) {
        respond(false, 'Invalid quantity: ' . $quantity);
    }

    // Check if user is logged in
    $user_id = null;
    if (isset($_SESSION['local_user_id'])) {
        $user_id = $_SESSION['local_user_id'];
    } elseif (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    } elseif (isset($_SESSION['firebase_user_id']) && isset($_SESSION['user_email'])) {
        // Sync Firebase user to database
        $user_id = syncFirebaseUserToDatabase(
            $_SESSION['firebase_user_id'],
            $_SESSION['user_email'],
            $_SESSION['user_name'] ?? null
        );
        if ($user_id) {
            $_SESSION['local_user_id'] = $user_id;
            $_SESSION['user_id'] = $user_id;
        }
    }

    if (!$user_id) {
        respond(false, 'Please login first to add items to cart');
    }

    // Verify product exists and get details
    $stmt = $conn->prepare("SELECT product_id, name, price, stock FROM products WHERE product_id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        respond(false, 'Product not found or not available');
    }

    // Check stock
    if ($product['stock'] < $quantity) {
        respond(false, 'Insufficient stock. Available: ' . $product['stock']);
    }

    // Get or create user's cart
    $cart_id = getOrCreateCart($user_id);

    if (!$cart_id) {
        respond(false, 'Unable to create cart. Please try again.');
    }

    // Check if item already exists in cart
    $stmt = $conn->prepare("SELECT cart_item_id, quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $existing_item = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_item) {
        // Update existing item
        $new_quantity = $existing_item['quantity'] + $quantity;

        // Check total stock
        if ($new_quantity > $product['stock']) {
            respond(false, 'Cannot add more items. Cart already has ' . $existing_item['quantity'] . ' items. Stock available: ' . $product['stock']);
        }

        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE cart_item_id = ?");
        $stmt->execute([$new_quantity, $existing_item['cart_item_id']]);

        $message = 'Cart updated! Quantity increased to ' . $new_quantity;
    } else {
        // Add new item
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
        $stmt->execute([$cart_id, $product_id, $quantity]);

        $message = 'Product added to cart successfully!';
    }

    // Get updated cart count
    $stmt = $conn->prepare("
        SELECT SUM(quantity) as total_items
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.cart_id
        WHERE c.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;

    error_log("Cart updated successfully. Total items: $cart_count");

    respond(true, $message, [
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity' => $quantity,
        'cart_count' => $cart_count,
        'price' => $product['price']
    ]);

} catch (PDOException $e) {
    error_log("Database error in add_to_cart.php: " . $e->getMessage());
    respond(false, 'Database error. Please try again.');
} catch (Exception $e) {
    error_log("Error in add_to_cart.php: " . $e->getMessage());
    respond(false, 'An error occurred. Please try again.');
}
?>
