<?php
/**
 * Simple Add to Cart - Reliable Version
 */

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');

// Fix the path - go up one level from ajax folder
require_once dirname(__DIR__) . '/includes/db_connect.php';

// Log all received data for debugging
error_log("ADD TO CART - POST data: " . print_r($_POST, true));
error_log("ADD TO CART - SESSION data: " . print_r($_SESSION, true));

try {
    // Simple response function
    function respond($success, $message, $data = []) {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'debug' => [
                'post_data' => $_POST,
                'session_user_id' => $_SESSION['user_id'] ?? 'not set',
                'session_firebase_user_id' => $_SESSION['firebase_user_id'] ?? 'not set',
                'session_local_user_id' => $_SESSION['local_user_id'] ?? 'not set',
                'session_keys' => array_keys($_SESSION)
            ]
        ];
        echo json_encode($response);
        exit;
    }

    // Simple cart creation function
    function getOrCreateCartSimple($user_id, $conn) {
        // Check if user has an existing cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($cart) {
            return $cart['cart_id'];
        }

        // Create new cart - check if table has created_at column
        try {
            // Try with created_at and updated_at columns
            $stmt = $conn->prepare("INSERT INTO carts (user_id, created_at, updated_at) VALUES (?, NOW(), NOW())");
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'created_at') !== false) {
                // Table doesn't have created_at column, use simple insert
                $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
                $stmt->execute([$user_id]);
            } else {
                throw $e;
            }
        }
        return $conn->lastInsertId();
    }

    // Get product ID and quantity
    $product_id = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);

    error_log("Product ID: $product_id, Quantity: $quantity");

    if ($product_id <= 0) {
        respond(false, "Invalid product ID: $product_id");
    }

    if ($quantity <= 0) {
        respond(false, "Invalid quantity: $quantity");
    }

    // Get user ID - simple approach
    $user_id = $_SESSION['user_id'] ?? $_SESSION['local_user_id'] ?? null;

    if (!$user_id) {
        respond(false, 'Please login first to add items to cart');
    }

    // Verify product exists and get details
    $stmt = $conn->prepare("SELECT product_id, name, price, stock FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        respond(false, 'Product not found or not available');
    }

    // Check stock
    if ($product['stock'] < $quantity) {
        respond(false, "Insufficient stock. Available: {$product['stock']}");
    }

    // Get or create user's cart
    $cart_id = getOrCreateCartSimple($user_id, $conn);

    if (!$cart_id) {
        respond(false, 'Unable to create cart. Please try again.');
    }

    // Check if item already exists in cart
    $stmt = $conn->prepare("SELECT cart_item_id, quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $existing_item = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_item) {
        // Update existing item
        $new_quantity = $existing_item['quantity'] + $quantity;

        // Check total stock
        if ($new_quantity > $product['stock']) {
            respond(false, "Cannot add more items. Cart already has {$existing_item['quantity']} items. Stock available: {$product['stock']}");
        }

        // Try to update with updated_at, fallback to simple update
        try {
            $stmt = $conn->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE cart_item_id = ?");
            $stmt->execute([$new_quantity, $existing_item['cart_item_id']]);
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'updated_at') !== false) {
                $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_item_id = ?");
                $stmt->execute([$new_quantity, $existing_item['cart_item_id']]);
            } else {
                throw $e;
            }
        }

        $message = "Cart updated! Quantity increased to $new_quantity";
    } else {
        // Add new item - try with timestamps, fallback to simple insert
        try {
            $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
            $stmt->execute([$cart_id, $product_id, $quantity]);
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'created_at') !== false || strpos($e->getMessage(), 'updated_at') !== false) {
                $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)");
                $stmt->execute([$cart_id, $product_id, $quantity]);
            } else {
                throw $e;
            }
        }

        $message = 'Product added to cart successfully!';
    }

    // Get updated cart count
    $stmt = $conn->prepare("
        SELECT SUM(quantity) as total_items
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.cart_id
        WHERE c.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;

    error_log("Cart updated successfully. Total items: $cart_count");

    respond(true, $message, [
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity' => $quantity,
        'cart_count' => $cart_count,
        'price' => $product['price']
    ]);

} catch (PDOException $e) {
    error_log("Database error in add_to_cart.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    respond(false, "Database error: {$e->getMessage()}");
} catch (Exception $e) {
    error_log("Error in add_to_cart.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    respond(false, "Error: {$e->getMessage()}");
}
