<?php
/**
 * Simple Test Add to Cart - For Debugging
 */

session_start();
header('Content-Type: application/json');

// Log all received data for debugging
error_log("TEST ADD TO CART - POST data: " . print_r($_POST, true));
error_log("TEST ADD TO CART - SESSION data: " . print_r($_SESSION, true));

try {
    // Simple response function
    function respond($success, $message, $data = []) {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'debug' => [
                'post_data' => $_POST,
                'session_user_id' => $_SESSION['user_id'] ?? 'not set',
                'session_firebase_user_id' => $_SESSION['firebase_user_id'] ?? 'not set',
                'session_keys' => array_keys($_SESSION)
            ]
        ];
        echo json_encode($response);
        exit;
    }
    
    // Get product ID
    $product_id = $_POST['product_id'] ?? 0;
    $quantity = $_POST['quantity'] ?? 1;
    
    error_log("Product ID: $product_id, Quantity: $quantity");
    
    if ($product_id <= 0) {
        respond(false, 'Invalid product ID: ' . $product_id);
    }
    
    // Check if user is logged in (any session variable)
    $user_logged_in = isset($_SESSION['user_id']) || isset($_SESSION['firebase_user_id']) || isset($_SESSION['local_user_id']);
    
    if (!$user_logged_in) {
        respond(false, 'Please login first. Available session keys: ' . implode(', ', array_keys($_SESSION)));
    }
    
    // Initialize session cart if not exists
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    // Add to session cart (simple approach)
    $found = false;
    foreach ($_SESSION['cart'] as &$item) {
        if ($item['product_id'] == $product_id) {
            $item['quantity'] += $quantity;
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        $_SESSION['cart'][] = [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'name' => 'Test Product ' . $product_id,
            'price' => 50000
        ];
    }
    
    // Count total items
    $total_items = 0;
    foreach ($_SESSION['cart'] as $item) {
        $total_items += $item['quantity'];
    }
    
    error_log("Cart updated. Total items: $total_items");
    
    respond(true, 'Product added to cart successfully!', [
        'product_id' => $product_id,
        'quantity' => $quantity,
        'cart_count' => $total_items,
        'cart_contents' => $_SESSION['cart']
    ]);
    
} catch (Exception $e) {
    error_log("Error in test_add_to_cart.php: " . $e->getMessage());
    respond(false, 'Error: ' . $e->getMessage());
}
?>
