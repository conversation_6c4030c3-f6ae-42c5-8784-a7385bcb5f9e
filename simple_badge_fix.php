<?php
/**
 * Super simple notification badge fix
 * This will work regardless of table structure
 */

session_start();

// Direct database connection
$host = 'localhost';
$dbname = 'db_tewuneed';
$username = 'root';
$password = '';

try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("❌ Connection failed: " . $e->getMessage());
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("❌ Please log in first. <a href='login.php'>Login here</a>");
}

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? 'check';

echo "<h1>🔔 Simple Badge Fix</h1>";
echo "<p>User ID: <strong>{$user_id}</strong></p>";

try {
    // Check if table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'order_notifications'");
    if ($stmt->rowCount() == 0) {
        echo "<div style='background: yellow; padding: 10px;'>⚠️ <strong>Table 'order_notifications' does not exist!</strong></div>";
        echo "<p>This means there are no notifications to clear. The badge might be showing due to a different issue.</p>";
        exit;
    }

    // Get table structure
    $stmt = $conn->query("DESCRIBE order_notifications");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 Table Structure</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Get all data for this user
    $stmt = $conn->prepare("SELECT * FROM order_notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $all_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Your Notifications (" . count($all_notifications) . " total)</h3>";
    
    if (count($all_notifications) > 0) {
        // Count unread
        $unread_count = 0;
        foreach ($all_notifications as $notif) {
            if (isset($notif['is_read']) && !$notif['is_read']) {
                $unread_count++;
            }
        }
        
        echo "<div style='background: " . ($unread_count > 0 ? 'lightcoral' : 'lightgreen') . "; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>🔴 Unread notifications causing badge: {$unread_count}</strong>";
        echo "</div>";
        
        // Show first few notifications
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach (array_keys($all_notifications[0]) as $column) {
            echo "<th>{$column}</th>";
        }
        echo "</tr>";
        
        $count = 0;
        foreach ($all_notifications as $notif) {
            if ($count >= 5) break; // Show only first 5
            echo "<tr>";
            foreach ($notif as $value) {
                $display_value = is_string($value) ? htmlspecialchars(substr($value, 0, 30)) : $value;
                if (strlen($value) > 30) $display_value .= "...";
                echo "<td>{$display_value}</td>";
            }
            echo "</tr>";
            $count++;
        }
        if (count($all_notifications) > 5) {
            echo "<tr><td colspan='" . count(array_keys($all_notifications[0])) . "'><em>... and " . (count($all_notifications) - 5) . " more</em></td></tr>";
        }
        echo "</table>";
        
        // Action buttons
        if ($action === 'check') {
            echo "<h3>🛠️ Fix Options</h3>";
            echo "<div style='margin: 20px 0;'>";
            echo "<a href='?action=mark_read' style='background: green; color: white; padding: 15px 20px; text-decoration: none; margin: 10px; border-radius: 5px; display: inline-block;'>✅ Mark All as Read</a>";
            echo "<a href='?action=delete' style='background: red; color: white; padding: 15px 20px; text-decoration: none; margin: 10px; border-radius: 5px; display: inline-block;' onclick='return confirm(\"Delete all notifications permanently?\")'>🗑️ Delete All</a>";
            echo "</div>";
        }
        
        // Perform actions
        if ($action === 'mark_read') {
            $stmt = $conn->prepare("UPDATE order_notifications SET is_read = 1 WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $affected = $stmt->rowCount();
            
            echo "<div style='background: lightgreen; padding: 20px; margin: 20px 0; border-radius: 5px; border: 2px solid green;'>";
            echo "<h3>✅ SUCCESS!</h3>";
            echo "<p>Marked <strong>{$affected}</strong> notifications as read.</p>";
            echo "<p>The badge should disappear now. If it doesn't, try refreshing the page.</p>";
            echo "</div>";
            
            echo "<script>";
            echo "setTimeout(function() { window.location.href = 'simple_badge_fix.php'; }, 3000);";
            echo "</script>";
        }
        
        if ($action === 'delete') {
            $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $affected = $stmt->rowCount();
            
            echo "<div style='background: lightcoral; padding: 20px; margin: 20px 0; border-radius: 5px; border: 2px solid red;'>";
            echo "<h3>🗑️ DELETED!</h3>";
            echo "<p>Permanently removed <strong>{$affected}</strong> notifications.</p>";
            echo "<p>The badge should disappear now. If it doesn't, try refreshing the page.</p>";
            echo "</div>";
            
            echo "<script>";
            echo "setTimeout(function() { window.location.href = 'simple_badge_fix.php'; }, 3000);";
            echo "</script>";
        }
        
    } else {
        echo "<div style='background: lightgreen; padding: 15px; border-radius: 5px;'>";
        echo "✅ <strong>No notifications found!</strong>";
        echo "<p>If the badge is still showing, the issue might be elsewhere. Try:</p>";
        echo "<ul>";
        echo "<li>Refreshing the page</li>";
        echo "<li>Clearing browser cache</li>";
        echo "<li>Checking if there's a different notification table</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Current status check
    $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread FROM order_notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $status = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 Current Status</h3>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Total notifications:</strong> {$status['total']}</p>";
    echo "<p><strong>Unread notifications:</strong> {$status['unread']}</p>";
    
    if ($status['unread'] == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Perfect! No unread notifications. Badge should be gone!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠️ Still {$status['unread']} unread notifications causing the badge.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: lightcoral; padding: 15px; border-radius: 5px;'>";
    echo "❌ <strong>Error:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links</h3>";
echo "<a href='index.php' style='margin: 5px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>🏠 Home</a>";
echo "<a href='simple_badge_fix.php' style='margin: 5px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>🔄 Refresh</a>";
echo "<a href='user_notifications.php' style='margin: 5px; padding: 10px; background: #ffc107; color: black; text-decoration: none; border-radius: 3px;'>🔔 Notifications Page</a>";

echo "<hr>";
echo "<h3>🧪 Instant Badge Hide</h3>";
echo "<button onclick='hideBadgeNow()' style='background: purple; color: white; padding: 15px; border: none; border-radius: 5px; cursor: pointer;'>🎯 Hide Badge Immediately</button>";

echo "<script>";
echo "function hideBadgeNow() {";
echo "  const badge = document.getElementById('headerNotificationBadge');";
echo "  if (badge) {";
echo "    badge.style.display = 'none';";
echo "    alert('✅ Badge hidden! If it reappears, there are still unread notifications in the database.');";
echo "  } else {";
echo "    alert('ℹ️ No badge element found. You might not be on a page with the header, or the badge is already hidden.');";
echo "  }";
echo "}";
echo "</script>";

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6;
}
table { 
    margin: 10px 0; 
    border-collapse: collapse;
    width: 100%;
}
th, td { 
    padding: 8px; 
    text-align: left; 
    border: 1px solid #ddd;
}
th { 
    background-color: #f2f2f2; 
    font-weight: bold;
}
h1, h2, h3 { 
    color: #333; 
}
</style>
