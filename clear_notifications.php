<?php
/**
 * Comprehensive Notification Reset System
 * Clears all types of notifications: session, cookies, database, and client-side
 */

session_start();
require_once 'includes/config.php';

// Get reset type from URL parameter
$reset_type = $_GET['type'] ?? 'all';
$user_id = $_SESSION['user_id'] ?? null;

$response = ['success' => false, 'message' => '', 'cleared' => []];

try {
    // 1. Clear session-based notifications
    if ($reset_type === 'all' || $reset_type === 'session') {
        $session_keys_to_clear = [
            'error', 'error_message', 'cart_error', 'stock_error',
            'flash_message', 'flash_type', 'notification', 'notification_type',
            'success_message', 'warning_message', 'info_message',
            'alert_message', 'alert_type', 'toast_message', 'toast_type',
            'order_success', 'payment_success', 'checkout_error'
        ];

        $cleared_sessions = 0;
        foreach ($session_keys_to_clear as $key) {
            if (isset($_SESSION[$key])) {
                unset($_SESSION[$key]);
                $cleared_sessions++;
            }
        }
        $response['cleared']['session'] = $cleared_sessions;
    }

    // 2. Clear cookie-based notifications
    if ($reset_type === 'all' || $reset_type === 'cookies') {
        $cookie_keys_to_clear = [
            'notification', 'error_message', 'success_message', 'cart_notification',
            'order_notification', 'payment_notification', 'stock_notification'
        ];

        $cleared_cookies = 0;
        foreach ($cookie_keys_to_clear as $key) {
            if (isset($_COOKIE[$key])) {
                setcookie($key, '', time() - 3600, '/');
                $cleared_cookies++;
            }
        }
        $response['cleared']['cookies'] = $cleared_cookies;
    }

    // 3. Clear database notifications (if user is logged in)
    if (($reset_type === 'all' || $reset_type === 'database') && $user_id) {
        $cleared_db = 0;

        // Clear order_notifications table
        try {
            $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $cleared_db += $stmt->rowCount();
        } catch (Exception $e) {
            // Table might not exist, continue
        }

        // Clear notifications table (if exists)
        try {
            $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $cleared_db += $stmt->rowCount();
        } catch (Exception $e) {
            // Table might not exist, continue
        }

        // Clear admin_notifications table (if user is admin)
        if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
            try {
                $stmt = $conn->prepare("DELETE FROM admin_notifications WHERE user_id = ? OR user_id IS NULL");
                $stmt->execute([$user_id]);
                $cleared_db += $stmt->rowCount();
            } catch (Exception $e) {
                // Table might not exist, continue
            }
        }

        $response['cleared']['database'] = $cleared_db;
    }

    // 4. Mark all notifications as read instead of deleting (alternative option)
    if ($reset_type === 'mark_read' && $user_id) {
        $marked_read = 0;

        // Mark order_notifications as read
        try {
            $stmt = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE user_id = ? AND is_read = FALSE");
            $stmt->execute([$user_id]);
            $marked_read += $stmt->rowCount();
        } catch (Exception $e) {
            // Table might not exist, continue
        }

        // Mark notifications as read
        try {
            $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0");
            $stmt->execute([$user_id]);
            $marked_read += $stmt->rowCount();
        } catch (Exception $e) {
            // Table might not exist, continue
        }

        $response['cleared']['marked_read'] = $marked_read;
    }

    $response['success'] = true;
    $response['message'] = 'Notifications cleared successfully';

} catch (Exception $e) {
    $response['message'] = 'Error clearing notifications: ' . $e->getMessage();
    error_log("Notification reset error: " . $e->getMessage());
}

// Handle different response formats
if (isset($_GET['format']) && $_GET['format'] === 'json') {
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Default: redirect to homepage with status message
if ($response['success']) {
    $_SESSION['success_message'] = 'All notifications have been cleared successfully.';
} else {
    $_SESSION['error_message'] = $response['message'];
}

$redirect_url = $_GET['redirect'] ?? 'index.php';
header('Location: ' . $redirect_url);
exit;
?>
