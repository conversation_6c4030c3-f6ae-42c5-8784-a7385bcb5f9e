/**
 * Fixed Cart Functionality for TeWuNeed
 * Handles add to cart, quantity updates, and cart management
 */

class CartManager {
    constructor() {
        this.isProcessing = false;
        this.init();
    }

    init() {
        console.log('🛒 CartManager initialized');
        this.setupEventListeners();
        this.updateCartDisplay();
    }

    setupEventListeners() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-to-cart-btn, .btn-add-to-cart')) {
                e.preventDefault();
                this.handleAddToCart(e);
            }
        });

        // Quantity controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quantity-plus')) {
                e.preventDefault();
                this.increaseQuantity(e.target);
            } else if (e.target.matches('.quantity-minus')) {
                e.preventDefault();
                this.decreaseQuantity(e.target);
            }
        });

        // Quantity input changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.quantity-input')) {
                this.validateQuantity(e.target);
            }
        });
    }

    async handleAddToCart(event) {
        if (this.isProcessing) {
            this.showNotification('Please wait, processing previous request...', 'warning');
            return;
        }

        const button = event.target.closest('button');
        const productCard = event.target.closest('.product-card, .product-detail');
        
        if (!productCard) {
            console.error('Product card not found');
            return;
        }

        // Get product data
        const productId = productCard.dataset.productId || 
                         productCard.querySelector('[data-product-id]')?.dataset.productId ||
                         button.dataset.productId;
        
        const quantityInput = productCard.querySelector('.quantity-input');
        const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;

        if (!productId) {
            this.showNotification('Product ID not found', 'error');
            console.error('Product ID not found in:', productCard);
            return;
        }

        console.log('Adding to cart:', { productId, quantity });

        // Disable button and show loading
        this.setButtonLoading(button, true);
        this.isProcessing = true;

        try {
            const response = await this.addToCartRequest(productId, quantity);
            
            if (response.success) {
                this.handleAddToCartSuccess(response, button);
            } else {
                this.handleAddToCartError(response.message || 'Failed to add to cart', button);
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            this.handleAddToCartError('Network error. Please try again.', button);
        } finally {
            this.setButtonLoading(button, false);
            this.isProcessing = false;
        }
    }

    async addToCartRequest(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        console.log('Sending request to ajax/add_to_cart.php with:', { productId, quantity });

        const response = await fetch('ajax/add_to_cart.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Server response:', data);
        return data;
    }

    handleAddToCartSuccess(response, button) {
        // Update cart count
        this.updateCartCount(response.cart_count);
        
        // Show success message
        this.showNotification(response.message, 'success');
        
        // Animate button
        this.animateButton(button);
        
        // Update cart badge
        this.animateCartBadge();

        console.log('Add to cart successful:', response);
    }

    handleAddToCartError(message, button) {
        this.showNotification(message, 'error');
        console.error('Add to cart failed:', message);
    }

    setButtonLoading(button, loading) {
        if (!button) return;

        if (loading) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || 'Add to Cart';
        }
    }

    animateButton(button) {
        if (!button) return;
        
        button.classList.add('btn-success');
        button.innerHTML = '<i class="fas fa-check"></i> Added!';
        
        setTimeout(() => {
            button.classList.remove('btn-success');
            button.innerHTML = button.dataset.originalText || 'Add to Cart';
        }, 2000);
    }

    animateCartBadge() {
        const cartBadge = document.querySelector('.cart-count, .cart-badge');
        if (cartBadge) {
            cartBadge.classList.add('animate-bounce');
            setTimeout(() => {
                cartBadge.classList.remove('animate-bounce');
            }, 600);
        }
    }

    updateCartCount(count) {
        const cartElements = document.querySelectorAll('.cart-count, .cart-badge');
        cartElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'inline' : 'none';
        });

        // Update page title
        if (count > 0) {
            document.title = `(${count}) ${document.title.replace(/^\(\d+\)\s/, '')}`;
        } else {
            document.title = document.title.replace(/^\(\d+\)\s/, '');
        }
    }

    increaseQuantity(button) {
        const input = button.parentElement.querySelector('.quantity-input');
        if (input) {
            const currentValue = parseInt(input.value) || 1;
            const maxValue = parseInt(input.max) || 999;
            if (currentValue < maxValue) {
                input.value = currentValue + 1;
                this.validateQuantity(input);
            }
        }
    }

    decreaseQuantity(button) {
        const input = button.parentElement.querySelector('.quantity-input');
        if (input) {
            const currentValue = parseInt(input.value) || 1;
            const minValue = parseInt(input.min) || 1;
            if (currentValue > minValue) {
                input.value = currentValue - 1;
                this.validateQuantity(input);
            }
        }
    }

    validateQuantity(input) {
        const value = parseInt(input.value) || 1;
        const min = parseInt(input.min) || 1;
        const max = parseInt(input.max) || 999;

        if (value < min) {
            input.value = min;
        } else if (value > max) {
            input.value = max;
            this.showNotification(`Maximum quantity is ${max}`, 'warning');
        }
    }

    updateCartDisplay() {
        // Update cart count from server if needed
        this.fetchCartCount();
    }

    async fetchCartCount() {
        try {
            const response = await fetch('ajax/get_cart_count.php', {
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateCartCount(data.cart_count);
                }
            }
        } catch (error) {
            console.log('Could not fetch cart count:', error);
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Remove existing notifications
        document.querySelectorAll('.cart-notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.className = `alert alert-${type} cart-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
        `;

        const icon = type === 'success' ? 'check-circle' : 
                    type === 'error' ? 'exclamation-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 'info-circle';

        notification.innerHTML = `
            <i class="fas fa-${icon} me-2"></i>${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });

        // Auto remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
}

// Initialize cart manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.cartManager = new CartManager();
    console.log('✅ Cart functionality loaded');
});

// Global function for backward compatibility
window.addToCart = function(event, productId, productName, price, imageUrl) {
    if (window.cartManager) {
        window.cartManager.handleAddToCart(event);
    } else {
        console.error('CartManager not initialized');
    }
};

// CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-bounce {
        animation: bounce 0.6s ease;
    }
    
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        80% { transform: translateY(-5px); }
    }
    
    .btn-success {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
    }
`;
document.head.appendChild(style);
