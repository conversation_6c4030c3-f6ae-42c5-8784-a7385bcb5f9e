<?php
// Start session
session_start();

// Set page variables for header
$page = 'home';
$page_title = 'Home';

// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load konfigurasi dan fungsi
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Get featured products - if no featured products, show recent products
try {
    // First try to get featured products if column exists
    $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                          COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                          c.name as category_name,
                          COALESCE(AVG(r.rating), 0) as rating,
                          COUNT(DISTINCT r.review_id) as review_count
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.category_id
                          LEFT JOIN product_reviews r ON p.product_id = r.product_id
                          WHERE p.is_active = 1
                          GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                          ORDER BY p.created_at DESC
                          LIMIT 8");
    $featuredProducts = $stmt->fetchAll();

    // Debug: Log featured products data
    error_log("Featured products count: " . count($featuredProducts));
    if (!empty($featuredProducts)) {
        error_log("First featured product: " . print_r($featuredProducts[0], true));
    }
} catch (PDOException $e) {
    // If is_featured column doesn't exist, get products without it
    try {
        $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                              0 as is_featured, p.is_active, p.created_at,
                              c.name as category_name,
                              COALESCE(AVG(r.rating), 0) as rating,
                              COUNT(DISTINCT r.review_id) as review_count
                              FROM products p
                              LEFT JOIN categories c ON p.category_id = c.category_id
                              LEFT JOIN product_reviews r ON p.product_id = r.product_id
                              WHERE p.is_active = 1
                              GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                              ORDER BY p.created_at DESC
                              LIMIT 8");
        $featuredProducts = $stmt->fetchAll();
    } catch (PDOException $e2) {
        error_log("Error fetching featured products: " . $e2->getMessage());
        $featuredProducts = [];
    }
}

// Get categories
try {
    $stmt = $conn->query("SELECT category_id, NAME as name, slug, description, image, is_active FROM categories WHERE (is_active = 1 OR is_active IS NULL) ORDER BY NAME ASC");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// Get recent products
try {
    $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                          COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                          c.name as category_name,
                          COALESCE(AVG(r.rating), 0) as rating,
                          COUNT(DISTINCT r.review_id) as review_count
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.category_id
                          LEFT JOIN product_reviews r ON p.product_id = r.product_id
                          WHERE p.is_active = 1
                          GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                          ORDER BY p.created_at DESC
                          LIMIT 12");
    $recentProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    // If is_featured column doesn't exist, get products without it
    try {
        $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                              0 as is_featured, p.is_active, p.created_at,
                              c.name as category_name,
                              COALESCE(AVG(r.rating), 0) as rating,
                              COUNT(DISTINCT r.review_id) as review_count
                              FROM products p
                              LEFT JOIN categories c ON p.category_id = c.category_id
                              LEFT JOIN product_reviews r ON p.product_id = r.product_id
                              WHERE p.is_active = 1
                              GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                              ORDER BY p.created_at DESC
                              LIMIT 12");
        $recentProducts = $stmt->fetchAll();
    } catch (PDOException $e2) {
        error_log("Error fetching recent products: " . $e2->getMessage());
        $recentProducts = [];
    }
}

// Get all products for the "All Products" section with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 24; // Products per page
$offset = ($page - 1) * $limit;

try {
    // Count total products
    $countStmt = $conn->query("SELECT COUNT(*) as total FROM products WHERE is_active = 1");
    $totalProducts = $countStmt->fetch()['total'];
    $totalPages = ceil($totalProducts / $limit);

    // Get products for current page
    $stmt = $conn->prepare("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                          COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                          c.name as category_name,
                          COALESCE(AVG(r.rating), 0) as rating,
                          COUNT(DISTINCT r.review_id) as review_count
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.category_id
                          LEFT JOIN product_reviews r ON p.product_id = r.product_id
                          WHERE p.is_active = 1
                          GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                          ORDER BY p.created_at DESC
                          LIMIT :limit OFFSET :offset");
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $allProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    // If is_featured column doesn't exist, try without it
    try {
        $stmt = $conn->prepare("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                              0 as is_featured, p.is_active, p.created_at,
                              c.name as category_name,
                              COALESCE(AVG(r.rating), 0) as rating,
                              COUNT(DISTINCT r.review_id) as review_count
                              FROM products p
                              LEFT JOIN categories c ON p.category_id = c.category_id
                              LEFT JOIN product_reviews r ON p.product_id = r.product_id
                              WHERE p.is_active = 1
                              GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                              ORDER BY p.created_at DESC
                              LIMIT :limit OFFSET :offset");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        $allProducts = $stmt->fetchAll();
    } catch (PDOException $e2) {
        $allProducts = [];
        $totalPages = 1;
        error_log("Database error: " . $e2->getMessage());
    }
}

// Get cart count
$cartCount = 0;
if (isset($_SESSION['user_id'])) {
    try {
        // Get from cart_items instead of direct cart table
        $stmt = $conn->prepare("
            SELECT SUM(quantity)
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $cartCount = $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        error_log("Error fetching cart count: " . $e->getMessage());
    }
}

include('includes/header.php');

// Session sudah dimulai di header.php
?>

<script>
// Add home-page class to body for CSS targeting
document.body.classList.add('home-page');

// Additional JavaScript to hide any "Home" text that might appear but preserve hero title
document.addEventListener('DOMContentLoaded', function() {
    // Hide any element containing "Home" text in the main content area but NOT hero title
    const mainContent = document.querySelector('main.main-content');
    if (mainContent) {
        const walker = document.createTreeWalker(
            mainContent,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim() === 'Home' &&
                node.parentElement.tagName === 'H1' &&
                !node.parentElement.classList.contains('hero-title')) {
                node.parentElement.style.display = 'none';
            }
        }
    }

    // Hide any page header that might contain "Home" but not hero section
    const pageHeaders = document.querySelectorAll('.page-header, .page-title');
    pageHeaders.forEach(header => {
        if (header.textContent.includes('Home') &&
            !header.closest('.hero-section')) {
            header.style.display = 'none';
        }
    });

    // Ensure hero title is visible
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        heroTitle.style.display = 'block';
        heroTitle.style.visibility = 'visible';
        heroTitle.style.opacity = '1';
    }
});
</script>

<style>
        /* TeWuNeed Brand Identity & Modern Design */
        :root {
            /* Brand Colors - TeWuNeed Identity */
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #ff6b6b;
            --success-color: #51cf66;
            --warning-color: #ffd43b;
            --info-color: #339af0;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --white: #ffffff;

            /* Gradients */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            --success-gradient: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            --hero-gradient: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);

            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
            --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
            --shadow-xl: 0 20px 60px rgba(0,0,0,0.2);

            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 20px;
            --radius-xl: 25px;
        }

        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--light-color);
        }

        /* Hero Section - Modern & Attractive */
        .hero-section {
            background: var(--hero-gradient),
                        url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: white;
            padding: 120px 0;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .hero-btn {
            background: var(--secondary-gradient);
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            box-shadow: 0 8px 25px rgba(245, 87, 108, 0.3);
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .hero-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .category-card {
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            height: 180px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: var(--card-shadow);
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--card-shadow-hover);
        }

        .category-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .category-card:hover .category-img {
            transform: scale(1.1);
        }

        .category-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .category-card:hover .category-overlay {
            opacity: 1;
        }

        .product-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            height: 100%;
            border: 0;
            border-radius: 20px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--card-shadow-hover);
        }

        .product-img {
            height: 220px;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .product-card:hover .product-img {
            transform: scale(1.05);
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--secondary-gradient);
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
            z-index: 2;
        }

        .out-of-stock-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .price-tag {
            background: var(--success-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            font-size: 1.3rem;
        }

        .btn-modern {
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
        }

        .btn-primary-modern {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary-modern:hover {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-modern {
            background: transparent;
            border: 2px solid;
            border-image: var(--primary-gradient) 1;
            color: #667eea;
        }

        .features-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
            padding: 80px 0;
        }

        .feature-card {
            text-align: center;
            padding: 2rem;
            border-radius: 20px;
            background: white;
            box-shadow: var(--card-shadow);
            transition: transform 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 20%;
            right: 10%;
            animation-delay: 4s;
        }

        /* FIXED: FORCE DROPDOWN TO BE VISIBLE AND CLICKABLE WITH HIGHEST Z-INDEX */
        .navbar {
            z-index: 99999 !important;
            position: relative !important;
        }

        .dropdown {
            z-index: 99999 !important;
            position: relative !important;
        }

        .dropdown-menu {
            display: none !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            z-index: 99999 !important;
            min-width: 200px !important;
            background: white !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 15px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
            padding: 0.5rem !important;
            margin-top: 10px !important;
        }

        .dropdown-menu.show {
            display: block !important;
            z-index: 99999 !important;
        }

        .dropdown-toggle {
            cursor: pointer !important;
            user-select: none !important;
            z-index: 99999 !important;
            position: relative !important;
        }

        .dropdown-toggle:hover {
            background-color: rgba(255,255,255,0.1) !important;
        }

        /* Ensure dropdown button is visible and clickable */
        #userDropdown {
            pointer-events: auto !important;
            cursor: pointer !important;
            z-index: 99999 !important;
            position: relative !important;
        }

        /* Ensure hero section doesn't interfere */
        .hero-section {
            z-index: 1 !important;
        }

        .hero-content {
            z-index: 2 !important;
        }

        /* Hide any potential "Home" text that might appear above hero */
        .page-header {
            display: none !important;
        }

        .page-title {
            display: none !important;
        }

        /* Ensure hero section starts immediately after navbar */
        .hero-section {
            margin-top: 0 !important;
            padding-top: 120px !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)),
                        url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80') !important;
            background-size: cover !important;
            background-position: center !important;
            background-attachment: fixed !important;
            color: white !important;
            padding: 120px 0 !important;
            margin-bottom: 0 !important;
            position: relative !important;
            overflow: hidden !important;
            min-height: 500px !important;
        }

        /* Hide any breadcrumb on home page */
        .breadcrumb {
            display: none !important;
        }

        /* Hide any potential page header elements but NOT hero title */
        h1.page-title,
        .page-header h1 {
            display: none !important;
        }

        /* Ensure main content starts clean but preserve hero title */
        main.main-content > .container:first-child > h1:first-child:not(.hero-title),
        main.main-content > h1:first-child:not(.hero-title) {
            display: none !important;
        }

        /* Make sure hero title is always visible */
        .hero-title {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: white !important;
            font-size: 3.5rem !important;
            font-weight: 800 !important;
            margin-bottom: 1.5rem !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
            animation: fadeInUp 1s ease-out !important;
            z-index: 10 !important;
            position: relative !important;
        }

        /* Force hide any element with "Home" text on homepage */
        body.home-page .page-header,
        body.home-page .breadcrumb,
        body.home-page h1:contains("Home") {
            display: none !important;
        }
</style>
    <!-- Include Modern Homepage CSS -->
    <link rel="stylesheet" href="css/modern-homepage.css">

    <!-- Include Modern Homepage JavaScript -->
    <script src="js/modern-homepage.js" defer></script>

    <!-- Modern Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            Welcome to <span class="brand-highlight">TeWuNeed</span>
                        </h1>
                        <p class="hero-subtitle">
                            Discover amazing products at unbeatable prices. Your one-stop destination for all your shopping needs.
                        </p>

                        <!-- Search Bar -->
                        <div class="hero-search mb-4">
                            <form action="products.php" method="GET" class="search-form">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control search-input"
                                           placeholder="Search for products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                                    <button class="btn search-btn" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Hero Stats -->
                        <div class="hero-stats">
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?php echo count($featuredProducts); ?>+</span>
                                <span class="hero-stat-label">Products</span>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number">1000+</span>
                                <span class="hero-stat-label">Happy Customers</span>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number">24/7</span>
                                <span class="hero-stat-label">Support</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="Shopping" class="img-fluid rounded-4 shadow-lg">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern Categories Section -->
    <section class="categories-section py-5">
        <div class="container">
            <div class="section-header text-center mb-5">
                <h2 class="section-title">Shop by Category</h2>
                <p class="section-subtitle">Explore our wide range of product categories</p>
            </div>

            <div class="row g-4">
                <?php
                // Get categories
                try {
                    $stmt = $conn->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name ASC LIMIT 6");
                    $categories = $stmt->fetchAll();

                    $category_icons = [
                        'Electronics' => 'fas fa-laptop',
                        'Fashion' => 'fas fa-tshirt',
                        'Home & Garden' => 'fas fa-home',
                        'Sports' => 'fas fa-dumbbell',
                        'Books' => 'fas fa-book',
                        'Health' => 'fas fa-heartbeat',
                        'Beauty' => 'fas fa-spa',
                        'Toys' => 'fas fa-gamepad',
                        'Automotive' => 'fas fa-car',
                        'Food' => 'fas fa-utensils'
                    ];

                    foreach ($categories as $index => $category):
                        $icon = $category_icons[$category['name']] ?? 'fas fa-tag';
                ?>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="products.php?category=<?php echo $category['category_id']; ?>" class="category-card">
                        <div class="category-icon">
                            <i class="<?php echo $icon; ?>"></i>
                        </div>
                        <h6 class="category-name"><?php echo htmlspecialchars($category['name']); ?></h6>
                    </a>
                </div>
                <?php endforeach; ?>
                } catch (Exception $e) {
                    // Fallback categories if database fails
                    $fallback_categories = [
                        ['name' => 'Electronics', 'icon' => 'fas fa-laptop'],
                        ['name' => 'Fashion', 'icon' => 'fas fa-tshirt'],
                        ['name' => 'Home & Garden', 'icon' => 'fas fa-home'],
                        ['name' => 'Sports', 'icon' => 'fas fa-dumbbell'],
                        ['name' => 'Books', 'icon' => 'fas fa-book'],
                        ['name' => 'Health', 'icon' => 'fas fa-heartbeat']
                    ];

                    foreach ($fallback_categories as $category):
                ?>
                <div class="col-lg-2 col-md-4 col-6">
                    <a href="products.php" class="category-card">
                        <div class="category-icon">
                            <i class="<?php echo $category['icon']; ?>"></i>
                        </div>
                        <h6 class="category-name"><?php echo $category['name']; ?></h6>
                    </a>
                </div>
                <?php endforeach; ?>
                <?php } ?>
            </div>
        </div>
    </section>

    <!-- Modern Featured Products Section -->
    <section class="featured-products-section py-5 bg-light">
        <div class="container">
            <div class="section-header text-center mb-5">
                <h2 class="section-title">Featured Products</h2>
                <p class="section-subtitle">Discover our handpicked selection of amazing products</p>
            </div>

            <div class="row g-4">
                <?php if (!empty($featuredProducts)): ?>
                    <?php foreach (array_slice($featuredProducts, 0, 8) as $product): ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="product-card">
                            <div class="product-image-container">
                                <?php if ($product['stock'] <= 0): ?>
                                    <div class="product-badge bg-secondary">Out of Stock</div>
                                <?php elseif ($product['is_featured']): ?>
                                    <div class="product-badge bg-warning">Featured</div>
                                <?php endif; ?>

                                <button class="product-wishlist">
                                    <i class="far fa-heart"></i>
                                </button>

                                <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     class="product-img">
                            </div>

                            <div class="product-info">
                                <div class="product-category"><?php echo htmlspecialchars($product['category_name'] ?? 'General'); ?></div>
                                <h6 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h6>

                                <div class="product-rating">
                                    <div class="rating-stars">
                                        <?php
                                        $rating = round($product['rating']);
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                        }
                                        ?>
                                    </div>
                                    <span class="rating-text">(<?php echo $product['review_count']; ?>)</span>
                                </div>

                                <div class="product-price">
                                    Rp <?php echo number_format($product['price'], 0, ',', '.'); ?>
                                </div>

                                <div class="product-actions">
                                    <?php if ($product['stock'] > 0): ?>
                                        <button class="btn btn-add-to-cart" onclick="addToCart(<?php echo $product['product_id']; ?>)">
                                            <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                        </button>
                                        <button class="btn btn-quick-view" onclick="quickView(<?php echo $product['product_id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-secondary" disabled>
                                            <i class="fas fa-times me-2"></i>Out of Stock
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <div class="empty-state">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No products available</h5>
                            <p class="text-muted">Check back later for new products!</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-5">
                <a href="products.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-th-large me-2"></i>View All Products
                </a>
            </div>
        </div>
    </section>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-5">
                <a href="Products.php" class="btn btn-primary-modern btn-lg px-5">
                    <i class="fas fa-arrow-right me-2"></i>View All Products
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Products -->
    <section class="container py-5">
        <div class="text-center mb-5">
            <h2 class="section-title">Latest Arrivals</h2>
            <p class="text-muted fs-5">Fresh products just added to our collection</p>
        </div>
        <div class="row g-4">
            <?php foreach (array_slice($recentProducts, 0, 8) as $product): ?>
            <div class="col-6 col-md-6 col-lg-3">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <span class="badge bg-success position-absolute top-0 start-0 m-3" style="z-index: 2; border-radius: 15px;">
                            <i class="fas fa-sparkles me-1"></i>New
                        </span>
                        <?php if($product['stock'] <= 0): ?>
                        <span class="out-of-stock-badge">
                            <i class="fas fa-times me-1"></i>Out of Stock
                        </span>
                        <?php endif; ?>
                        <img src="<?= !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80' ?>"
                             class="card-img-top product-img" alt="<?php echo isset($product['name']) ? htmlspecialchars($product['name']) : 'Product'; ?>"
                             onerror="this.src='https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo isset($product['name']) ? htmlspecialchars($product['name']) : 'Unknown Product'; ?></h5>
                        <p class="card-text small text-muted mb-2">
                            <i class="fas fa-tag me-1"></i><?php echo isset($product['category_name']) ? htmlspecialchars($product['category_name']) : 'No Category'; ?>
                        </p>
                        <p class="card-text flex-grow-1 text-muted">
                            <?php echo isset($product['description']) ? substr(htmlspecialchars($product['description']), 0, 80) . '...' : 'No description available'; ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center mt-auto mb-3">
                            <span class="price-tag">Rp <?php echo isset($product['price']) ? number_format($product['price'], 0, ',', '.') : '0'; ?></span>
                            <span class="badge bg-<?php echo (isset($product['stock']) && $product['stock'] > 0) ? 'success' : 'danger'; ?> rounded-pill">
                                <?php if(isset($product['stock']) && $product['stock'] > 0): ?>
                                    <i class="fas fa-check me-1"></i>In Stock
                                <?php else: ?>
                                    <i class="fas fa-times me-1"></i>Out of Stock
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-modern flex-fill details-btn" data-product-id="<?php echo isset($product['product_id']) ? $product['product_id'] : '0'; ?>">
                                <i class="fas fa-eye me-1"></i>Details
                            </button>
                            <?php if(isset($product['stock']) && $product['stock'] > 0): ?>
                            <button class="btn btn-primary-modern flex-fill add-to-cart-btn"
                                   data-product-id="<?php echo $product['product_id']; ?>"
                                   data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                   data-product-price="<?php echo $product['price']; ?>"
                                   data-product-image="<?php echo !empty($product['image']) ? $product['image'] : 'product-default.jpg'; ?>">
                                <i class="fas fa-cart-plus me-1"></i>Add
                            </button>
                            <?php else: ?>
                            <button class="btn btn-secondary btn-modern flex-fill" disabled>
                                <i class="fas fa-times me-1"></i>Unavailable
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- All Products Section -->
    <section class="container py-5">
        <div class="text-center mb-5">
            <h2 class="section-title">All Products</h2>
            <p class="text-muted fs-5">Browse our complete collection of <?php echo $totalProducts; ?> products</p>
        </div>
        <div class="row g-4">
            <?php foreach ($allProducts as $product): ?>
            <div class="col-6 col-md-6 col-lg-3">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <?php if ($product['is_featured']): ?>
                        <span class="featured-badge">
                            <i class="fas fa-star me-1"></i>Featured
                        </span>
                        <?php endif; ?>
                        <?php if ($product['stock'] <= 0): ?>
                        <span class="out-of-stock-badge">
                            <i class="fas fa-times me-1"></i>Out of Stock
                        </span>
                        <?php endif; ?>
                        <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                             class="card-img-top product-img" alt="<?php echo htmlspecialchars($product['name']); ?>"
                             onerror="this.src='https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                        <p class="card-text small text-muted mb-2">
                            <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($product['category_name']); ?>
                        </p>
                        <p class="card-text flex-grow-1 text-muted">
                            <?php echo substr(htmlspecialchars($product['description']), 0, 80) . '...'; ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="price-tag">Rp <?php echo number_format($product['price'], 0, ',', '.'); ?></span>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-modern detail-link"
                                        data-product-id="<?php echo $product['product_id']; ?>">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($product['stock'] > 0): ?>
                                <button class="btn btn-sm btn-primary-modern add-to-cart-btn"
                                        data-product-id="<?php echo $product['product_id']; ?>"
                                        data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                        data-product-price="<?php echo $product['price']; ?>"
                                        data-product-image="<?php echo !empty($product['image']) ? $product['image'] : 'product-default.jpg'; ?>">
                                    <i class="fas fa-cart-plus"></i>
                                </button>
                                <?php else: ?>
                                <button class="btn btn-sm btn-secondary btn-modern" disabled>
                                    <i class="fas fa-times"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-center mt-5">
            <nav aria-label="Product pagination">
                <ul class="pagination">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>

        <div class="text-center mt-4">
            <a href="Products.php" class="btn btn-primary btn-lg px-5 py-3" style="border-radius: 50px;">
                <i class="fas fa-th-large me-2"></i>View All Products
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Why Choose TEWUNEED?</h2>
                <p class="text-muted fs-5">We provide the best shopping experience for our customers</p>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Free Shipping</h4>
                        <p class="text-muted">Free delivery on orders over Rp 500,000. Fast and reliable shipping nationwide.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Secure Payment</h4>
                        <p class="text-muted">Your payment information is protected with bank-level security encryption.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-undo-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Easy Returns</h4>
                        <p class="text-muted">Not satisfied? Return your items within 7 days for a full refund.</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="fw-bold mb-3">24/7 Support</h4>
                        <p class="text-muted">Our customer service team is always ready to help you anytime.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-5" style="background: var(--primary-gradient);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 text-white">
                    <h3 class="fw-bold mb-3">Stay Updated with TEWUNEED</h3>
                    <p class="mb-0">Subscribe to our newsletter and get exclusive deals, new product announcements, and special offers delivered to your inbox.</p>
                </div>
                <div class="col-lg-6">
                    <div class="d-flex gap-3 mt-3 mt-lg-0">
                        <input type="email" class="form-control form-control-lg" placeholder="Enter your email address" style="border-radius: 25px; border: none;">
                        <button class="btn btn-light btn-lg px-4" style="border-radius: 25px; font-weight: 600;">
                            <i class="fas fa-paper-plane me-2"></i>Subscribe
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Add to Cart Modal -->
    <div class="modal fade" id="addToCartModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Product Added to Cart</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success fs-1 me-3"></i>
                        <div>
                            <p class="mb-0">The product has been added to your cart successfully!</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Continue Shopping</button>
                    <a href="cart.php" class="btn btn-primary">View Cart</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal fade" id="productDetailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalProductTitle">Product Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <img id="modalProductImage" src="" class="img-fluid mb-3" alt="Product Image">
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <span class="badge bg-secondary" id="modalProductCategory"></span>
                                <span id="modalProductStock" class="ms-2"></span>
                            </div>
                            <h4 id="modalProductPrice" class="text-primary mb-3"></h4>
                            <p><strong>Description:</strong></p>
                            <p id="modalProductDescription"></p>

                            <div class="mt-4">
                                <div class="d-flex align-items-center mb-3">
                                    <label for="modalQuantity" class="me-3">Quantity:</label>
                                    <div class="input-group" style="width: 150px;">
                                        <button type="button" class="btn btn-outline-secondary" id="decreaseQuantity">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" id="modalQuantity" class="form-control text-center" value="1" min="1">
                                        <button type="button" class="btn btn-outline-secondary" id="increaseQuantity">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <button type="button" id="modalAddToCartBtn" class="btn btn-primary">
                                    <i class="fas fa-cart-plus me-2"></i> Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="js/disable-error-notifications.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- Include Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Include Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script>
    // Show alert function using Toastify
    function showAlert(type, message) {
        console.log('showAlert called:', type, message);

        // Use Toastify if available
        if (typeof Toastify !== 'undefined') {
            let background = '';
            switch(type) {
                case 'success':
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
                    break;
                case 'danger':
                    background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
                    break;
                case 'warning':
                    background = 'linear-gradient(to right, #f46b45, #eea849)';
                    break;
                default:
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
            }

            Toastify({
                text: message,
                duration: 3000,
                close: true,
                gravity: "top",
                position: "right",
                backgroundColor: background,
                stopOnFocus: true
            }).showToast();
        } else {
            // Fallback to alert
            alert(message);
        }
    }

    // Enhanced addToCart function with better notification
    function addToCartWithNotification(productId, productName, quantity) {
        console.log('addToCartWithNotification called:', productName, quantity);

        // Validate inputs
        if (!productName || productName === 'undefined' || productName === 'null' || productName === '') {
            console.warn('Product name is invalid:', productName);
            productName = 'Produk';
        }

        if (!quantity || isNaN(quantity)) {
            console.warn('Quantity is invalid:', quantity);
            quantity = 1;
        }

        // Create a more informative message
        const message = `✅ ${quantity} ${productName} berhasil ditambahkan ke keranjang`;
        showAlert('success', message);

        // Optional: Show a brief visual feedback on the button
        const button = $(`.add-to-cart-btn[data-product-id="${productId}"]`);
        if (button.length) {
            const originalText = button.html();
            button.html('<i class="fas fa-check text-success"></i>');
            setTimeout(() => {
                button.html(originalText);
            }, 1500);
        }
    }

    // Debug logging
    console.log('Homepage notification system loaded');
    console.log('Toastify available:', typeof Toastify !== 'undefined');

    // Debug: Check all add-to-cart buttons and their data
    $(document).ready(function() {
        console.log('=== DEBUGGING PRODUCT DATA ===');
        $('.add-to-cart-btn').each(function(index) {
            const $btn = $(this);
            console.log(`Button ${index + 1}:`, {
                productId: $btn.data('product-id'),
                productName: $btn.data('product-name'),
                productPrice: $btn.data('product-price'),
                productImage: $btn.data('product-image'),
                allData: $btn.data()
            });
        });
        console.log('=== END DEBUGGING ===');
    });

    $(document).ready(function() {
        // Add to cart functionality
        $(document).on('click', '.add-to-cart-btn', function() {
            var productId = $(this).data('product-id');
            var productName = $(this).data('product-name');
            var productPrice = $(this).data('product-price');
            var productImage = $(this).data('product-image');
            var button = $(this);

            // Debug logging
            console.log('Add to cart clicked:');
            console.log('Product ID:', productId);
            console.log('Product Name:', productName);
            console.log('Product Price:', productPrice);
            console.log('Button data attributes:', $(this).data());

            // Validate product ID first
            if (!productId || productId === 'undefined' || productId === 'null' || productId === '') {
                console.error('Product ID is missing');
                showAlert('danger', 'Error: Product ID tidak valid');
                return;
            }

            // Validate and fix product name
            if (!productName || productName === 'undefined' || productName === 'null' || productName === '') {
                console.warn('Product name is missing or invalid, trying to get from card title');
                // Try to get product name from the card title
                const cardTitle = $(this).closest('.card').find('.card-title').text().trim();
                if (cardTitle) {
                    productName = cardTitle;
                    console.log('Found product name from card title:', productName);
                } else {
                    // Try to get from h5 element
                    const h5Title = $(this).closest('.card').find('h5').text().trim();
                    if (h5Title) {
                        productName = h5Title;
                        console.log('Found product name from h5:', productName);
                    } else {
                        productName = 'Produk';
                        console.warn('Could not find product name, using fallback');
                    }
                }
            }

            // Store original button content
            var originalContent = button.html();

            // Show loading state
            button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');
            button.prop('disabled', true);

            // Add to cart via AJAX
            $.ajax({
                url: 'ajax/add_to_cart.php',
                method: 'POST',
                data: {
                    product_id: productId,
                    quantity: 1
                },
                dataType: 'json',
                success: function(response) {
                    console.log('AJAX Response:', response);

                    if (response.success) {
                        // Update cart count in UI
                        updateCartCount();

                        // Use product name from server response (more reliable)
                        const serverProductName = response.product_name || productName || 'Produk';
                        const serverQuantity = response.quantity || 1;
                        const totalQuantity = response.total_quantity || serverQuantity;

                        console.log('AJAX Success - Server Product Name:', serverProductName);
                        console.log('AJAX Success - Server Quantity:', serverQuantity);
                        console.log('AJAX Success - Total Quantity in Cart:', totalQuantity);

                        // Show success message with quantity and correct product name
                        addToCartWithNotification(productId, serverProductName, serverQuantity);

                        // If this was an update (total_quantity exists and is different), show additional info
                        if (response.total_quantity && response.total_quantity > serverQuantity) {
                            setTimeout(() => {
                                showAlert('info', `Total ${serverProductName} di keranjang: ${totalQuantity}`);
                            }, 2000);
                        }
                    } else {
                        // Show error message
                        showAlert('danger', 'Error: ' + (response.message || 'Gagal menambahkan ke keranjang'));
                    }

                    // Restore button
                    setTimeout(function() {
                        button.html(originalContent);
                        button.prop('disabled', false);
                    }, 600);
                },
                error: function(xhr, status, error) {
                    console.error("Error adding to cart:", xhr.responseText);
                    showAlert('danger', 'Error adding to cart. Please try again.');

                    // Restore button
                    setTimeout(function() {
                        button.html(originalContent);
                        button.prop('disabled', false);
                    }, 600);
                }
            });
        });



        // Update cart count function
        function updateCartCount() {
            $.ajax({
                url: 'ajax/get_cart_count.php',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    const cartCountBadge = $('.position-absolute.badge');
                    if (response.count > 0) {
                        cartCountBadge.text(response.count);
                        cartCountBadge.show();
                    } else {
                        cartCountBadge.hide();
                    }
                }
            });
        }

        // Product detail popup
        $(document).on('click', '.detail-link, .details-btn', function(e) {
            e.preventDefault();
            const productId = $(this).data('product-id');

            // Show loading state
            $('#modalProductTitle').text('Loading...');
            $('#modalProductPrice').text('');
            $('#modalProductCategory').text('');
            $('#modalProductStock').text('');
            $('#modalProductDescription').text('');
            $('#modalProductImage').attr('src', 'assets/img/default-product.jpg');

            // Fetch product details
            $.ajax({
                url: 'ajax/get_product.php',
                data: { id: productId },
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    $('#modalProductTitle').text(response.name);
                    $('#modalProductPrice').text('Rp ' + new Intl.NumberFormat('id-ID').format(response.price));
                    $('#modalProductCategory').text(response.category);

                    // Set stock status with appropriate styling
                    if (parseInt(response.stock) > 0) {
                        $('#modalProductStock').html('<span class="text-success"><i class="fas fa-check-circle"></i> Tersedia (' + response.stock + ' unit)</span>');
                    } else {
                        $('#modalProductStock').html('<span class="text-danger"><i class="fas fa-times-circle"></i> Stok Habis</span>');
                    }

                    $('#modalProductDescription').text(response.description);

                    // Handle image path correctly
                    if (response.image && response.image !== 'default-product.jpg') {
                        $('#modalProductImage').attr('src', 'uploads/' + response.image);
                    } else {
                        $('#modalProductImage').attr('src', 'assets/img/product-default.jpg');
                    }

                    // Add error handling for image loading
                    $('#modalProductImage').on('error', function() {
                        $(this).attr('src', 'assets/img/product-default.jpg');
                    });

                    // Setup add to cart button
                    if(parseInt(response.stock) > 0) {
                        $('#modalAddToCartBtn').prop('disabled', false);
                        $('#modalAddToCartBtn').data('product-id', response.id);
                        $('#modalAddToCartBtn').data('product-name', response.name);
                        $('#modalAddToCartBtn').data('product-price', response.price);
                        $('#modalAddToCartBtn').data('product-image', response.image);

                        // Reset quantity
                        $('#modalQuantity').val(1);
                        $('#modalQuantity').attr('max', response.stock);
                        $('#modalQuantity').prop('disabled', false);
                    } else {
                        $('#modalAddToCartBtn').prop('disabled', true);
                        $('#modalQuantity').prop('disabled', true);
                    }
                },
                error: function(xhr, status, error) {
                    $('#modalProductTitle').text('Error loading product');
                    $('#modalProductDescription').text('Failed to load product details. Please try again. Error: ' + error);
                    console.error("Error loading product:", xhr.responseText);
                }
            });

            $('#productDetailModal').modal('show');
        });

        // Quantity controls
        $('#decreaseQuantity').click(function() {
            var value = parseInt($('#modalQuantity').val());
            if (value > 1) {
                $('#modalQuantity').val(value - 1);
            }
        });

        $('#increaseQuantity').click(function() {
            var value = parseInt($('#modalQuantity').val());
            var max = parseInt($('#modalQuantity').attr('max'));
            if (value < max) {
                $('#modalQuantity').val(value + 1);
            }
        });

        // Handle modal add to cart button
        $('#modalAddToCartBtn').on('click', function() {
            const productId = $(this).data('product-id');
            const productName = $(this).data('product-name') || $('#modalProductTitle').text();
            const quantity = parseInt($('#modalQuantity').val()) || 1;

            console.log('Modal Add to Cart - Product ID:', productId);
            console.log('Modal Add to Cart - Product Name:', productName);
            console.log('Modal Add to Cart - Quantity:', quantity);

            // Validate inputs
            if (!productId) {
                showAlert('danger', 'Error: Product ID tidak valid');
                return;
            }

            if (quantity <= 0) {
                showAlert('danger', 'Error: Quantity harus lebih dari 0');
                return;
            }

            $(this).prop('disabled', true);
            $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menambahkan...');

            // Add to cart via AJAX
            $.ajax({
                url: 'ajax/add_to_cart.php',
                method: 'POST',
                data: {
                    product_id: productId,
                    quantity: quantity
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Modal AJAX Response:', response);

                    if (response.success) {
                        // Update cart count
                        updateCartCount();

                        // Use product name from server response (more reliable)
                        const serverProductName = response.product_name || productName || 'Produk';
                        const serverQuantity = response.quantity || quantity;
                        const totalQuantity = response.total_quantity || serverQuantity;

                        // Close modal and show success message with quantity
                        $('#productDetailModal').modal('hide');

                        // Show success notification
                        if (quantity > 1) {
                            addToCartWithNotification(productId, serverProductName, quantity);
                        } else {
                            addToCartWithNotification(productId, serverProductName, serverQuantity);
                        }

                        // If this was an update, show additional info
                        if (response.total_quantity && response.total_quantity > serverQuantity) {
                            setTimeout(() => {
                                showAlert('info', `Total ${serverProductName} di keranjang: ${totalQuantity}`);
                            }, 2000);
                        }
                    } else {
                        // Show error in modal
                        showAlert('danger', 'Error: ' + (response.message || 'Gagal menambahkan ke keranjang'));
                    }

                    // Reset button
                    $('#modalAddToCartBtn').prop('disabled', false);
                    $('#modalAddToCartBtn').html('<i class="fas fa-cart-plus me-2"></i> Add to Cart');
                },
                error: function(xhr, status, error) {
                    console.error("Error adding to cart:", xhr.responseText);
                    showAlert('danger', 'Error adding to cart. Please try again.');

                    // Reset button
                    $('#modalAddToCartBtn').prop('disabled', false);
                    $('#modalAddToCartBtn').html('<i class="fas fa-cart-plus me-2"></i> Add to Cart');
                }
            });
        });
    });

    // SIMPLE DROPDOWN FIX - Direct approach
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Index page loaded - Simple dropdown fix');

        // Wait for everything to load
        setTimeout(function() {
            const userDropdown = document.getElementById('userDropdown');

            if (userDropdown) {
                console.log('✅ Found user dropdown button');

                // Remove any existing event listeners
                userDropdown.replaceWith(userDropdown.cloneNode(true));
                const newDropdown = document.getElementById('userDropdown');

                // Add simple click handler
                newDropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('🖱️ Dropdown clicked!');

                    const menu = this.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {

                        // Close all other dropdowns first
                        document.querySelectorAll('.dropdown-menu').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.remove('show');
                            }
                        });

                        // Toggle this dropdown
                        const isOpen = menu.classList.contains('show');
                        if (isOpen) {
                            menu.classList.remove('show');
                            this.setAttribute('aria-expanded', 'false');
                            console.log('� Dropdown closed');
                        } else {
                            menu.classList.add('show');
                            this.setAttribute('aria-expanded', 'true');
                            console.log('📥 Dropdown opened');
                        }
                    } else {
                        console.log('❌ Dropdown menu not found');
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!newDropdown.contains(e.target)) {
                        const menu = newDropdown.nextElementSibling;
                        if (menu && menu.classList.contains('show')) {
                            menu.classList.remove('show');
                            newDropdown.setAttribute('aria-expanded', 'false');
                            console.log('📤 Dropdown closed (outside click)');
                        }
                    }
                });

                console.log('✅ Simple dropdown handler attached - ready for user interaction');

            } else {
                console.log('ℹ️ User dropdown not found - user may not be logged in');

                // Debug: Look for any dropdown elements
                const allDropdowns = document.querySelectorAll('[id*="ropdown"], [class*="ropdown"]');
                console.log('All dropdown-related elements found:', allDropdowns.length);
                allDropdowns.forEach((el, index) => {
                    console.log(`Dropdown ${index}:`, el.id, el.className);
                });

                // Check if user session exists
                const userElements = document.querySelectorAll('[class*="user"], [id*="user"]');
                console.log('User-related elements found:', userElements.length);
                userElements.forEach((el, index) => {
                    console.log(`User element ${index}:`, el.id, el.className, el.textContent?.substring(0, 50));
                });
            }
        }, 500);
    });
    </script>

    <!-- Modern Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="section-header text-center mb-5">
                <h2 class="section-title">Why Choose TeWuNeed?</h2>
                <p class="section-subtitle">We provide the best shopping experience for our customers</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h5 class="feature-title">Fast Delivery</h5>
                        <p class="feature-description">Quick and reliable delivery to your doorstep</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="feature-title">Secure Payment</h5>
                        <p class="feature-description">Your payment information is safe and secure</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="feature-title">24/7 Support</h5>
                        <p class="feature-description">Round-the-clock customer support</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-undo"></i>
                        </div>
                        <h5 class="feature-title">Easy Returns</h5>
                        <p class="feature-description">Hassle-free return and exchange policy</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modern Newsletter Section -->
    <section class="newsletter-section py-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h3 class="newsletter-title">Stay Updated!</h3>
                    <p class="newsletter-subtitle">Subscribe to our newsletter for the latest deals and updates</p>
                </div>
                <div class="col-lg-6">
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Enter your email address">
                            <button class="btn btn-light" type="submit">
                                <i class="fas fa-paper-plane me-2"></i>Subscribe
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced JavaScript for Modern Features -->
    <script>
        // Add to cart functionality
        function addToCart(productId) {
            if (!productId) {
                alert('Product ID is required');
                return;
            }

            // Check if user is logged in
            <?php if (!isset($_SESSION['user_id'])): ?>
                alert('Please login to add items to cart');
                window.location.href = 'login.php';
                return;
            <?php endif; ?>

            // Add to cart via AJAX
            $.ajax({
                url: 'ajax/add_to_cart.php',
                method: 'POST',
                data: {
                    product_id: productId,
                    quantity: 1
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Show success notification
                        showNotification('Product added to cart successfully!', 'success');
                        // Update cart count if function exists
                        if (typeof updateCartCount === 'function') {
                            updateCartCount();
                        }
                    } else {
                        showNotification(response.message || 'Failed to add product to cart', 'error');
                    }
                },
                error: function() {
                    showNotification('Error adding product to cart', 'error');
                }
            });
        }

        // Quick view functionality
        function quickView(productId) {
            // Implement quick view modal
            alert('Quick view for product ' + productId + ' - Feature coming soon!');
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} notification-toast`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Newsletter subscription
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                this.reset();
            } else {
                showNotification('Please enter a valid email address', 'error');
            }
        });

        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .notification-toast {
                animation: slideInRight 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>

<?php include('includes/footer.php'); ?>
