<?php
// Start session
session_start();

// Set page variables for header
$page = 'home';
$page_title = 'Home';

// Load konfigurasi dan fungsi
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Get featured products and recent products
try {
    // Get featured products first
    $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                          COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                          c.name as category_name,
                          COALESCE(AVG(r.rating), 0) as rating,
                          COUNT(DISTINCT r.review_id) as review_count
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.category_id
                          LEFT JOIN product_reviews r ON p.product_id = r.product_id
                          WHERE p.is_active = 1 AND COALESCE(p.is_featured, 0) = 1
                          GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                          ORDER BY p.created_at DESC
                          LIMIT 8");
    $featuredProducts = $stmt->fetchAll();

    // If no featured products, get recent products
    if (empty($featuredProducts)) {
        $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                              COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                              c.name as category_name,
                              COALESCE(AVG(r.rating), 0) as rating,
                              COUNT(DISTINCT r.review_id) as review_count
                              FROM products p
                              LEFT JOIN categories c ON p.category_id = c.category_id
                              LEFT JOIN product_reviews r ON p.product_id = r.product_id
                              WHERE p.is_active = 1
                              GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                              ORDER BY p.created_at DESC
                              LIMIT 12");
        $featuredProducts = $stmt->fetchAll();
    }

    // Get recent products for additional section
    $stmt = $conn->query("SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image,
                          COALESCE(p.is_featured, 0) as is_featured, p.is_active, p.created_at,
                          c.name as category_name,
                          COALESCE(AVG(r.rating), 0) as rating,
                          COUNT(DISTINCT r.review_id) as review_count
                          FROM products p
                          LEFT JOIN categories c ON p.category_id = c.category_id
                          LEFT JOIN product_reviews r ON p.product_id = r.product_id
                          WHERE p.is_active = 1
                          GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, p.is_active, p.created_at, c.name
                          ORDER BY p.created_at DESC
                          LIMIT 16");
    $recentProducts = $stmt->fetchAll();

} catch (PDOException $e) {
    $featuredProducts = [];
    $recentProducts = [];
}

// Get cart count
$cartCount = 0;
if (isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->prepare("
            SELECT SUM(quantity)
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $cartCount = $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        // Ignore error
    }
}

include 'includes/header.php';
?>

<!-- Include Modern Homepage CSS -->
<link rel="stylesheet" href="css/modern-homepage.css">

<!-- Include Modern Homepage JavaScript -->
<script src="js/modern-homepage.js" defer></script>

<style>
/* TeWuNeed Brand Identity & Modern Design - Blue Theme */
:root {
    /* Brand Colors - TeWuNeed Blue Identity */
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --white: #ffffff;

    /* Blue Gradients */
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --secondary-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --hero-gradient: linear-gradient(135deg, rgba(37, 99, 235, 0.95) 0%, rgba(29, 78, 216, 0.95) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
    --shadow-xl: 0 20px 60px rgba(0,0,0,0.2);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 25px;
}

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

/* Hero Section - Modern & Attractive */
.hero-section {
    background: var(--hero-gradient),
                url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--white);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white"><path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"/></svg>');
    background-size: cover;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.brand-highlight {
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 400;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    max-width: 500px;
}

.hero-search {
    max-width: 500px;
}

.search-input {
    border: none;
    border-radius: var(--radius-xl);
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: var(--shadow-lg);
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

.search-btn {
    background: var(--accent-color);
    border: none;
    border-radius: var(--radius-lg);
    padding: 15px 25px;
    color: var(--white);
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #ff5252;
    transform: scale(1.05);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 2rem;
    font-weight: 800;
    display: block;
    color: var(--white);
}

.hero-stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    color: var(--white);
}

.hero-image img {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

/* Section Headers */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* Category Cards */
.categories-section {
    padding: 80px 0;
    background: var(--white);
}

.category-card {
    display: block;
    text-decoration: none;
    color: var(--dark-color);
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 30px 20px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    color: var(--dark-color);
    text-decoration: none;
}

.category-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-name {
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

/* Product Cards */
.featured-products-section {
    padding: 80px 0;
}

.recent-products-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* Special Deals Section */
.special-deals-section {
    background: var(--primary-gradient) !important;
    position: relative;
    overflow: hidden;
}

.special-deals-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="white" opacity="0.05"><circle cx="50" cy="50" r="40"/></svg>');
    background-size: 100px 100px;
}

.deal-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: var(--dark-color);
}

.deal-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.deal-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #ef4444;
    color: white;
    padding: 8px 15px;
    border-radius: 0 var(--radius-lg) 0 var(--radius-lg);
    font-weight: 700;
    font-size: 0.8rem;
    transform: rotate(10deg);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.deal-info {
    margin-top: 15px;
}

.deal-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.deal-price {
    margin-bottom: 15px;
}

.current-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ef4444;
    display: block;
}

.original-price {
    font-size: 1rem;
    color: #6b7280;
    text-decoration: line-through;
    margin-top: 5px;
}

.deal-timer {
    background: #fef3c7;
    color: #92400e;
    padding: 8px 15px;
    border-radius: var(--radius-xl);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: inline-block;
}

.btn-deal {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: var(--radius-xl);
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-deal:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    color: white;
}

.product-card {
    background: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    height: 220px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: var(--radius-xl);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-wishlist {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-color);
    transition: all 0.3s ease;
    z-index: 2;
}

.product-wishlist:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.product-info {
    padding: 20px;
}

.product-category {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.rating-stars {
    color: var(--warning-color);
    font-size: 0.9rem;
}

.rating-text {
    color: #6c757d;
    font-size: 0.85rem;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 15px;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-md);
    padding: 10px 15px;
    font-weight: 600;
    color: var(--white);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-quick-view {
    background: var(--white);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    padding: 10px 12px;
    transition: all 0.3s ease;
}

.btn-quick-view:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.feature-card {
    padding: 40px 20px;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* Newsletter Section */
.newsletter-section {
    background: var(--primary-gradient) !important;
}

.newsletter-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.newsletter-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.newsletter-form .form-control {
    border: none;
    border-radius: var(--radius-lg);
    padding: 15px 20px;
    font-size: 1rem;
}

.newsletter-form .btn {
    border-radius: var(--radius-lg);
    padding: 15px 25px;
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        gap: 1rem;
    }
    
    .hero-stat-number {
        font-size: 1.5rem;
    }
}
</style>

<!-- Modern Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Welcome to <span class="brand-highlight">TeWuNeed</span>
                    </h1>
                    <p class="hero-subtitle">
                        Discover amazing products at unbeatable prices. Your one-stop destination for all your shopping needs.
                    </p>

                    <!-- Search Bar -->
                    <div class="hero-search mb-4">
                        <form action="products.php" method="GET" class="search-form">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control search-input"
                                       placeholder="Search for products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                                <button class="btn search-btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Hero Stats -->
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <span class="hero-stat-number"><?php echo count($featuredProducts); ?>+</span>
                            <span class="hero-stat-label">Products</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">1000+</span>
                            <span class="hero-stat-label">Happy Customers</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">24/7</span>
                            <span class="hero-stat-label">Support</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                         alt="Shopping" class="img-fluid rounded-4 shadow-lg">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Categories Section -->
<section class="categories-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our wide range of product categories</p>
        </div>

        <div class="row g-4">
            <?php
            // Simple category display
            $categories = [
                ['category_id' => 1, 'name' => 'Electronics', 'icon' => 'fas fa-laptop'],
                ['category_id' => 2, 'name' => 'Fashion', 'icon' => 'fas fa-tshirt'],
                ['category_id' => 3, 'name' => 'Home & Garden', 'icon' => 'fas fa-home'],
                ['category_id' => 4, 'name' => 'Sports', 'icon' => 'fas fa-dumbbell'],
                ['category_id' => 5, 'name' => 'Books', 'icon' => 'fas fa-book'],
                ['category_id' => 6, 'name' => 'Health', 'icon' => 'fas fa-heartbeat']
            ];

            foreach ($categories as $category) {
                echo '<div class="col-lg-2 col-md-4 col-6">';
                echo '<a href="products.php?category=' . $category['category_id'] . '" class="category-card">';
                echo '<div class="category-icon"><i class="' . $category['icon'] . '"></i></div>';
                echo '<h6 class="category-name">' . htmlspecialchars($category['name']) . '</h6>';
                echo '</a>';
                echo '</div>';
            }
            ?>
        </div>
    </div>
</section>

<!-- Modern Featured Products Section -->
<section class="featured-products-section py-5 bg-light">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Featured Products</h2>
            <p class="section-subtitle">Discover our handpicked selection of amazing products</p>
        </div>

        <div class="row g-4">
            <?php if (!empty($featuredProducts)): ?>
                <?php foreach (array_slice($featuredProducts, 0, 8) as $product): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="product-image-container">
                            <?php if ($product['stock'] <= 0): ?>
                                <div class="product-badge bg-secondary">Out of Stock</div>
                            <?php elseif ($product['is_featured']): ?>
                                <div class="product-badge bg-warning">Featured</div>
                            <?php endif; ?>

                            <button class="product-wishlist">
                                <i class="far fa-heart"></i>
                            </button>

                            <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 class="product-img">
                        </div>

                        <div class="product-info">
                            <div class="product-category"><?php echo htmlspecialchars($product['category_name'] ?? 'General'); ?></div>
                            <h6 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h6>

                            <div class="product-rating">
                                <div class="rating-stars">
                                    <?php
                                    $rating = round($product['rating']);
                                    for ($i = 1; $i <= 5; $i++) {
                                        echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                    }
                                    ?>
                                </div>
                                <span class="rating-text">(<?php echo $product['review_count']; ?>)</span>
                            </div>

                            <div class="product-price">
                                Rp <?php echo number_format($product['price'], 0, ',', '.'); ?>
                            </div>

                            <div class="product-actions">
                                <?php if ($product['stock'] > 0): ?>
                                    <button class="btn btn-add-to-cart" onclick="addToCart(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                    <button class="btn btn-quick-view" onclick="quickView(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <div class="empty-state">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No products available</h5>
                        <p class="text-muted">Check back later for new products!</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-5">
            <a href="products.php" class="btn btn-primary btn-lg">
                <i class="fas fa-th-large me-2"></i>View All Products
            </a>
        </div>
    </div>
</section>

<!-- Recent Products Section -->
<section class="recent-products-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Latest Products</h2>
            <p class="section-subtitle">Check out our newest arrivals and trending items</p>
        </div>

        <div class="row g-4">
            <?php if (!empty($recentProducts)): ?>
                <?php foreach (array_slice($recentProducts, 0, 12) as $product): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="product-image-container">
                            <?php if ($product['stock'] <= 0): ?>
                                <div class="product-badge bg-secondary">Out of Stock</div>
                            <?php elseif (strtotime($product['created_at']) > strtotime('-7 days')): ?>
                                <div class="product-badge bg-success">New</div>
                            <?php endif; ?>

                            <button class="product-wishlist">
                                <i class="far fa-heart"></i>
                            </button>

                            <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 class="product-img">
                        </div>

                        <div class="product-info">
                            <div class="product-category"><?php echo htmlspecialchars($product['category_name'] ?? 'General'); ?></div>
                            <h6 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h6>

                            <div class="product-rating">
                                <div class="rating-stars">
                                    <?php
                                    $rating = round($product['rating']);
                                    for ($i = 1; $i <= 5; $i++) {
                                        echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                    }
                                    ?>
                                </div>
                                <span class="rating-text">(<?php echo $product['review_count']; ?>)</span>
                            </div>

                            <div class="product-price">
                                Rp <?php echo number_format($product['price'], 0, ',', '.'); ?>
                            </div>

                            <div class="product-actions">
                                <?php if ($product['stock'] > 0): ?>
                                    <button class="btn btn-add-to-cart" onclick="addToCart(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                    <button class="btn btn-quick-view" onclick="quickView(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <div class="empty-state">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No recent products available</h5>
                        <p class="text-muted">Check back later for new arrivals!</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-5">
            <a href="products.php?sort=newest" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-clock me-2"></i>View All Recent Products
            </a>
        </div>
    </div>
</section>

<!-- Special Deals Section -->
<section class="special-deals-section py-5 bg-primary text-white">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title text-white">Special Deals</h2>
            <p class="section-subtitle text-white opacity-75">Limited time offers you don't want to miss!</p>
        </div>

        <div class="row g-4">
            <?php if (!empty($recentProducts)): ?>
                <?php
                // Show first 4 products as special deals
                foreach (array_slice($recentProducts, 0, 4) as $product):
                    // Calculate fake discount for demo
                    $originalPrice = $product['price'] * 1.2;
                    $discount = 20;
                ?>
                <div class="col-lg-3 col-md-6">
                    <div class="deal-card">
                        <div class="deal-badge">
                            <span class="discount-percent"><?php echo $discount; ?>% OFF</span>
                        </div>

                        <div class="product-image-container">
                            <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 class="product-img">
                        </div>

                        <div class="deal-info">
                            <h6 class="deal-title"><?php echo htmlspecialchars($product['name']); ?></h6>

                            <div class="deal-price">
                                <span class="current-price">Rp <?php echo number_format($product['price'], 0, ',', '.'); ?></span>
                                <span class="original-price">Rp <?php echo number_format($originalPrice, 0, ',', '.'); ?></span>
                            </div>

                            <div class="deal-timer">
                                <i class="fas fa-clock me-1"></i>
                                <span class="timer-text">Limited Time!</span>
                            </div>

                            <button class="btn btn-deal" onclick="addToCart(<?php echo $product['product_id']; ?>)">
                                <i class="fas fa-bolt me-2"></i>Grab Deal
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Modern Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Why Choose TeWuNeed?</h2>
            <p class="section-subtitle">We provide the best shopping experience for our customers</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h5 class="feature-title">Fast Delivery</h5>
                    <p class="feature-description">Quick and reliable delivery to your doorstep</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="feature-title">Secure Payment</h5>
                    <p class="feature-description">Your payment information is safe and secure</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5 class="feature-title">24/7 Support</h5>
                    <p class="feature-description">Round-the-clock customer support</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h5 class="feature-title">Easy Returns</h5>
                    <p class="feature-description">Hassle-free return and exchange policy</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Newsletter Section -->
<section class="newsletter-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="newsletter-title">Stay Updated!</h3>
                <p class="newsletter-subtitle">Subscribe to our newsletter for the latest deals and updates</p>
            </div>
            <div class="col-lg-6">
                <form class="newsletter-form">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter your email address">
                        <button class="btn btn-light" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Subscribe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
// Add to cart functionality
function addToCart(productId) {
    if (!productId) {
        alert('Product ID is required');
        return;
    }

    // Check if user is logged in
    <?php if (!isset($_SESSION['user_id'])): ?>
        alert('Please login to add items to cart');
        window.location.href = 'login.php';
        return;
    <?php endif; ?>

    // Add to cart via AJAX
    $.ajax({
        url: 'ajax/add_to_cart.php',
        method: 'POST',
        data: {
            product_id: productId,
            quantity: 1
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Show success notification
                showNotification('Product added to cart successfully!', 'success');
                // Update cart count if function exists
                if (typeof updateCartCount === 'function') {
                    updateCartCount();
                }
            } else {
                showNotification(response.message || 'Failed to add product to cart', 'error');
            }
        },
        error: function() {
            showNotification('Error adding product to cart', 'error');
        }
    });
}

// Quick view functionality
function quickView(productId) {
    // Implement quick view modal
    alert('Quick view for product ' + productId + ' - Feature coming soon!');
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease;
    `;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// Newsletter subscription
document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                this.reset();
            } else {
                showNotification('Please enter a valid email address', 'error');
            }
        });
    }
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    .notification-toast {
        animation: slideInRight 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
