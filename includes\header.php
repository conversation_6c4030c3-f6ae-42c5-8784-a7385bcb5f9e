<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/firebase_auth.php';
require_once __DIR__ . '/functions.php';

// Get cart count and notification count from database
$cart_count = 0;
$notification_count = 0;
if (isset($_SESSION['user_id'])) {
    try {
        require_once __DIR__ . '/db_connect.php';
        if ($conn) {
            // Get cart count
            $stmt = $conn->prepare("
                SELECT SUM(ci.quantity) as total_items
                FROM cart_items ci
                JOIN carts c ON ci.cart_id = c.cart_id
                WHERE c.user_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $cart_count = $result['total_items'] ?? 0;

            // Get unread notification count
            try {
                $stmt = $conn->prepare("
                    SELECT COUNT(*) as unread_count
                    FROM order_notifications
                    WHERE user_id = ? AND is_read = FALSE
                ");
                $stmt->execute([$_SESSION['user_id']]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $notification_count = $result['unread_count'] ?? 0;
            } catch (Exception $e) {
                // Table might not exist yet
                $notification_count = 0;
            }
        }
    } catch (Exception $e) {
        error_log("Error getting cart/notification count: " . $e->getMessage());
        $cart_count = 0;
        $notification_count = 0;
    }
}

// Get user info if logged in
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// Debug logging removed for production
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/favicon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Dropdown Z-Index Fix CSS -->
    <link href="css/dropdown-fix.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">

    <!-- Modern Blue Header Styles -->
    <style>
        /* TeWuNeed Modern Blue Header */
        :root {
            --header-primary: #2563eb;
            --header-secondary: #1d4ed8;
            --header-accent: #3b82f6;
            --header-light: #f8fafc;
            --header-white: #ffffff;
        }

        .navbar {
            background: linear-gradient(135deg, var(--header-primary) 0%, var(--header-secondary) 100%) !important;
            padding: 1rem 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-brand {
            font-size: 2rem !important;
            font-weight: 800 !important;
            letter-spacing: 1px;
            color: var(--header-white) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .navbar-nav .nav-link {
            font-weight: 600;
            margin: 0 0.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            color: rgba(255, 255, 255, 0.9) !important;
            padding: 0.75rem 1.25rem !important;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
            color: var(--header-white) !important;
        }

        .navbar-nav .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
            color: var(--header-white) !important;
        }

        .btn-outline-light {
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: var(--header-white) !important;
            padding: 0.5rem 1.5rem;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
            color: var(--header-white) !important;
        }

        .cart-count, .notification-count {
            font-size: 0.7rem;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Enhanced Search Bar */
        .search-container {
            position: relative;
            max-width: 400px;
        }

        .search-input {
            border: none;
            border-radius: 25px;
            padding: 0.75rem 3rem 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: var(--header-white);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
            outline: none;
            color: var(--header-white);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--header-white);
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

        /* User Dropdown */
        .user-dropdown {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .user-dropdown:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* FIXED: Enhanced dropdown styling with higher z-index */
        .navbar {
            z-index: 9999 !important;
            position: relative !important;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            margin-top: 10px;
            min-width: 220px;
            padding: 0.5rem;
            z-index: 9999 !important;
            position: absolute !important;
        }

        .dropdown {
            z-index: 9999 !important;
            position: relative !important;
        }

        .dropdown-item {
            border-radius: 10px;
            margin: 2px 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .dropdown-header {
            font-weight: 600;
            color: #6c757d;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-color: rgba(0,0,0,0.1);
        }

        /* Mobile responsive */
        @media (max-width: 991.98px) {
            .navbar-nav .nav-link {
                margin: 0.25rem 0;
                text-align: center;
            }

            .navbar-collapse {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                margin-top: 1rem;
                padding: 1rem;
                backdrop-filter: blur(10px);
            }

            .dropdown-menu {
                position: static !important;
                float: none !important;
                width: auto !important;
                margin-top: 0 !important;
                background-color: rgba(255, 255, 255, 0.95) !important;
                border: 1px solid rgba(0,0,0,0.1) !important;
                box-shadow: inset 0 1px 0 rgba(255,255,255,0.1) !important;
            }
        }
    </style>



    <!-- Cancelled orders styling -->
    <link rel="stylesheet" href="css/cancelled-orders.css">
</head>
<body>
    <!-- Alert Container -->
    <div id="alert-container" class="position-fixed top-0 end-0 p-3" style="z-index: 9996;"></div>

    <!-- Loading Spinner -->
    <div id="spinner" class="position-fixed top-50 start-50 translate-middle" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- Modern Blue Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-store me-2"></i>TeWuNeed
                </a>

                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarMain">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page ?? '') === 'home' ? 'active' : ''; ?>" href="index.php">
                               <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page ?? '') === 'products' ? 'active' : ''; ?>" href="products_public.php">
                               <i class="fas fa-shopping-bag me-1"></i>Products
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page ?? '') === 'about' ? 'active' : ''; ?>" href="about.php">
                               <i class="fas fa-info-circle me-1"></i>About
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page ?? '') === 'contact' ? 'active' : ''; ?>" href="contact.php">
                               <i class="fas fa-envelope me-1"></i>Contact
                            </a>
                        </li>
                        <?php if (isset($_SESSION['user_id']) || isset($_SESSION['firebase_user_id'])): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($page ?? '') === 'order_tracking' ? 'active' : ''; ?>" href="order_tracking.php">
                               <i class="fas fa-truck me-1"></i>Track Orders
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>

                    <!-- Enhanced Search Bar -->
                    <div class="search-container me-3 d-none d-lg-block">
                        <form action="products_public.php" method="get" class="position-relative">
                            <input class="form-control search-input" type="search" name="search"
                                   placeholder="Search products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button class="search-btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- Notifications (only for logged in users) -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="notifications.php" class="btn btn-outline-light position-relative me-2" title="Notifikasi">
                            <i class="fas fa-bell"></i>
                            <?php if ($notification_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning cart-count" id="headerNotificationBadge">
                                <?php echo $notification_count; ?>
                            </span>
                            <?php endif; ?>
                        </a>
                        <?php endif; ?>

                        <!-- Cart -->
                        <a href="cart.php" class="btn btn-outline-light position-relative me-2" title="Keranjang">
                            <i class="fas fa-shopping-cart"></i>
                            <?php if ($cart_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count">
                                <?php echo $cart_count; ?>
                            </span>
                            <?php endif; ?>
                        </a>

                        <?php if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle d-flex align-items-center"
                                    type="button"
                                    id="userDropdown"
                                    data-bs-toggle="dropdown"
                                    data-bs-auto-close="true"
                                    aria-expanded="false"
                                    style="border-radius: 25px; font-weight: 600;">
                                <i class="fas fa-user me-2"></i>
                                <span class="d-none d-md-inline">
                                    <?php
                                    $display_name = 'User';
                                    if (isset($_SESSION['user_name']) && !empty($_SESSION['user_name'])) {
                                        $display_name = $_SESSION['user_name'];
                                    } elseif (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                                        $display_name = $_SESSION['full_name'];
                                    } elseif (isset($_SESSION['username']) && !empty($_SESSION['username'])) {
                                        $display_name = $_SESSION['username'];
                                    }
                                    echo htmlspecialchars($display_name);
                                    ?>
                                </span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-lg"
                                aria-labelledby="userDropdown"
                                style="border-radius: 15px; border: none; min-width: 200px; margin-top: 10px;">
                                <li>
                                    <h6 class="dropdown-header text-muted">
                                        <i class="fas fa-user-circle me-2"></i>Account Menu
                                    </h6>
                                </li>
                                <li><a class="dropdown-item py-2" href="profile.php">
                                    <i class="fas fa-user-circle me-2 text-primary"></i>My Profile
                                </a></li>
                                <li><a class="dropdown-item py-2" href="order.php">
                                    <i class="fas fa-shopping-bag me-2 text-success"></i>My Orders
                                </a></li>
                                <li><a class="dropdown-item py-2" href="user_notifications.php">
                                    <i class="fas fa-bell me-2 text-warning"></i>Notifications
                                    <?php if ($notification_count > 0): ?>
                                    <span class="badge bg-warning text-dark ms-1"><?php echo $notification_count; ?></span>
                                    <?php endif; ?>
                                </a></li>
                                <li><a class="dropdown-item py-2" href="wishlist.php">
                                    <i class="fas fa-heart me-2 text-danger"></i>Wishlist
                                </a></li>
                                <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item py-2" href="admin/dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2 text-warning"></i>Admin Dashboard
                                </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item py-2 text-danger" href="#" onclick="handleLogout(event)">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </div>
                        <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2" style="border-radius: 25px; font-weight: 600;">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span class="d-none d-md-inline">Login</span>
                        </a>
                        <a href="register.php" class="btn btn-light" style="border-radius: 25px; font-weight: 600; color: #667eea;">
                            <i class="fas fa-user-plus me-1"></i>
                            <span class="d-none d-md-inline">Register</span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Firebase Configuration for Header -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="js/firebase-config.js"></script>

    <!-- Logout Handler Script -->
    <script>
    // Handle logout for both Firebase and regular users
    function handleLogout(event) {
        event.preventDefault();

        // Check if Firebase is available and user is signed in
        if (typeof firebase !== 'undefined' && firebase.auth && firebase.auth().currentUser) {
            // Firebase logout
            firebase.auth().signOut().then(() => {
                console.log('Firebase user signed out');
                // Clear backend session
                return fetch('logout.php', {
                    method: 'POST',
                    credentials: 'same-origin'
                });
            }).then(response => response.json())
            .then(data => {
                console.log('Backend session cleared');
                window.location.href = 'login.php';
            }).catch((error) => {
                console.error('Error during logout:', error);
                // Fallback: redirect to logout.php
                window.location.href = 'logout.php';
            });
        } else {
            // Regular logout
            window.location.href = 'logout.php';
        }
    }
    </script>

    <!-- Real-time notification update script -->
    <?php if (isset($_SESSION['user_id'])): ?>
    <script>
    // Update notification count in header
    function updateHeaderNotificationCount() {
        fetch('ajax/get_order_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                last_update: 0
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.unread_count !== undefined) {
                const badge = document.getElementById('headerNotificationBadge');
                const bellIcon = document.querySelector('a[href="user_notifications.php"] i');

                if (data.unread_count > 0) {
                    if (badge) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline';
                    } else {
                        // Create badge if it doesn't exist
                        const notificationLink = document.querySelector('a[href="user_notifications.php"]');
                        if (notificationLink) {
                            const newBadge = document.createElement('span');
                            newBadge.id = 'headerNotificationBadge';
                            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning cart-count';
                            newBadge.textContent = data.unread_count;
                            notificationLink.appendChild(newBadge);
                        }
                    }

                    // Add animation to bell icon
                    if (bellIcon) {
                        bellIcon.classList.add('fa-shake');
                        setTimeout(() => {
                            bellIcon.classList.remove('fa-shake');
                        }, 1000);
                    }
                } else {
                    if (badge) {
                        badge.style.display = 'none';
                    }
                }

                // Update dropdown notification count
                const dropdownNotificationLink = document.querySelector('a[href="user_notifications.php"] .badge');
                if (dropdownNotificationLink) {
                    if (data.unread_count > 0) {
                        dropdownNotificationLink.textContent = data.unread_count;
                        dropdownNotificationLink.style.display = 'inline';
                    } else {
                        dropdownNotificationLink.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error updating notification count:', error);
        });
    }

    // Update notification count on page load and periodically
    document.addEventListener('DOMContentLoaded', function() {
        updateHeaderNotificationCount();

        // Update every 30 seconds
        setInterval(updateHeaderNotificationCount, 30000);
    });
    </script>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <?php if (isset($page_title) && isset($page) && $page !== 'home'): ?>
        <div class="page-header bg-light py-4">
            <div class="container">
                <h1 class="page-title"><?php echo $page_title; ?></h1>
                <?php if (isset($page_description)): ?>
                <p class="page-description text-muted"><?php echo $page_description; ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

